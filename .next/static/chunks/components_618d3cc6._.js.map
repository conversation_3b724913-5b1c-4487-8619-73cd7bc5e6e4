{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Development/protec/web/components/providers/auth-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport { SessionProvider } from \"next-auth/react\"\nimport { ReactNode } from \"react\"\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  return <SessionProvider>{children}</SessionProvider>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,qBAAO,8VAAC,sWAAA,CAAA,kBAAe;kBAAE;;;;;;AAC3B;KAFgB", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Development/protec/web/components/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\nimport { type ThemeProviderProps } from \"next-themes/dist/types\"\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8VAAC,2RAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KAFgB", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Development/protec/web/components/providers/trpc-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\nimport { httpBatchLink } from '@trpc/client'\nimport { createTRPCReact } from '@trpc/react-query'\nimport { useState } from 'react'\nimport superjson from 'superjson'\n\nimport { type AppRouter } from '@/lib/trpc/root'\n\nconst createQueryClient = () =>\n  new QueryClient({\n    defaultOptions: {\n      queries: {\n        staleTime: 30 * 1000,\n      },\n    },\n  })\n\nlet clientQueryClientSingleton: QueryClient | undefined = undefined\n\nconst getQueryClient = () => {\n  if (typeof window === 'undefined') {\n    // Server: always make a new query client\n    return createQueryClient()\n  }\n  // Browser: use singleton pattern to keep the same query client\n  return (clientQueryClientSingleton ??= createQueryClient())\n}\n\nexport const api = createTRPCReact<AppRouter>()\n\nexport function TRPCReactProvider(props: { children: React.ReactNode }) {\n  const queryClient = getQueryClient()\n\n  const [trpcClient] = useState(() =>\n    api.createClient({\n      links: [\n        httpBatchLink({\n          url: getBaseUrl() + '/api/trpc',\n          transformer: superjson,\n        }),\n      ],\n    })\n  )\n\n  return (\n    <api.Provider client={trpcClient} queryClient={queryClient}>\n      <QueryClientProvider client={queryClient}>\n        {props.children}\n      </QueryClientProvider>\n    </api.Provider>\n  )\n}\n\nfunction getBaseUrl() {\n  if (typeof window !== 'undefined') return window.location.origin\n  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`\n  return `http://localhost:${process.env.PORT ?? 3000}`\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;AANA;;;;;;AAUA,MAAM,oBAAoB,IACxB,IAAI,yPAAA,CAAA,cAAW,CAAC;QACd,gBAAgB;YACd,SAAS;gBACP,WAAW,KAAK;YAClB;QACF;IACF;AAEF,IAAI,6BAAsD;AAE1D,MAAM,iBAAiB;IACrB,uCAAmC;;IAGnC;IACA,+DAA+D;IAC/D,OAAQ,+BAA+B;AACzC;AAEO,MAAM,MAAM,CAAA,GAAA,8XAAA,CAAA,kBAAe,AAAD;AAE1B,SAAS,kBAAkB,KAAoC;;IACpE,MAAM,cAAc;IAEpB,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,8TAAA,CAAA,WAAQ,AAAD;sCAAE,IAC5B,IAAI,YAAY,CAAC;gBACf,OAAO;oBACL,CAAA,GAAA,qUAAA,CAAA,gBAAa,AAAD,EAAE;wBACZ,KAAK,eAAe;wBACpB,aAAa,mMAAA,CAAA,UAAS;oBACxB;iBACD;YACH;;IAGF,qBACE,8VAAC,IAAI,QAAQ;QAAC,QAAQ;QAAY,aAAa;kBAC7C,cAAA,8VAAC,yRAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAC1B,MAAM,QAAQ;;;;;;;;;;;AAIvB;GArBgB;KAAA;AAuBhB,SAAS;IACP,wCAAmC,OAAO,OAAO,QAAQ,CAAC,MAAM;;AAGlE", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Development/protec/web/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,2RAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8VAAC,2QAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,2RAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}]}