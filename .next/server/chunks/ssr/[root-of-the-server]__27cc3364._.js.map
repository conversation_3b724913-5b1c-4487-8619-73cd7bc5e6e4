{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Development/protec/web/components/providers/auth-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport { SessionProvider } from \"next-auth/react\"\nimport { ReactNode } from \"react\"\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  return <SessionProvider>{children}</SessionProvider>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,qBAAO,+YAAC,mWAAA,CAAA,kBAAe;kBAAE;;;;;;AAC3B", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Development/protec/web/components/providers/theme-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\"\nimport { type ThemeProviderProps } from \"next-themes/dist/types\"\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,+YAAC,wRAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Development/protec/web/components/providers/trpc-provider.tsx"], "sourcesContent": ["\"use client\"\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\nimport { httpBatchLink } from '@trpc/client'\nimport { createTRPCReact } from '@trpc/react-query'\nimport { useState } from 'react'\nimport superjson from 'superjson'\n\nimport { type AppRouter } from '@/lib/trpc/root'\n\nconst createQueryClient = () =>\n  new QueryClient({\n    defaultOptions: {\n      queries: {\n        staleTime: 30 * 1000,\n      },\n    },\n  })\n\nlet clientQueryClientSingleton: QueryClient | undefined = undefined\n\nconst getQueryClient = () => {\n  if (typeof window === 'undefined') {\n    // Server: always make a new query client\n    return createQueryClient()\n  }\n  // Browser: use singleton pattern to keep the same query client\n  return (clientQueryClientSingleton ??= createQueryClient())\n}\n\nexport const api = createTRPCReact<AppRouter>()\n\nexport function TRPCReactProvider(props: { children: React.ReactNode }) {\n  const queryClient = getQueryClient()\n\n  const [trpcClient] = useState(() =>\n    api.createClient({\n      links: [\n        httpBatchLink({\n          url: getBaseUrl() + '/api/trpc',\n          transformer: superjson,\n        }),\n      ],\n    })\n  )\n\n  return (\n    <api.Provider client={trpcClient} queryClient={queryClient}>\n      <QueryClientProvider client={queryClient}>\n        {props.children}\n      </QueryClientProvider>\n    </api.Provider>\n  )\n}\n\nfunction getBaseUrl() {\n  if (typeof window !== 'undefined') return window.location.origin\n  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`\n  return `http://localhost:${process.env.PORT ?? 3000}`\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAUA,MAAM,oBAAoB,IACxB,IAAI,sPAAA,CAAA,cAAW,CAAC;QACd,gBAAgB;YACd,SAAS;gBACP,WAAW,KAAK;YAClB;QACF;IACF;AAEF,IAAI,6BAAsD;AAE1D,MAAM,iBAAiB;IACrB,wCAAmC;QACjC,yCAAyC;QACzC,OAAO;IACT;;AAGF;AAEO,MAAM,MAAM,CAAA,GAAA,2XAAA,CAAA,kBAAe,AAAD;AAE1B,SAAS,kBAAkB,KAAoC;IACpE,MAAM,cAAc;IAEpB,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,sWAAA,CAAA,WAAQ,AAAD,EAAE,IAC5B,IAAI,YAAY,CAAC;YACf,OAAO;gBACL,CAAA,GAAA,kUAAA,CAAA,gBAAa,AAAD,EAAE;oBACZ,KAAK,eAAe;oBACpB,aAAa,gMAAA,CAAA,UAAS;gBACxB;aACD;QACH;IAGF,qBACE,+YAAC,IAAI,QAAQ;QAAC,QAAQ;QAAY,aAAa;kBAC7C,cAAA,+YAAC,sRAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAC1B,MAAM,QAAQ;;;;;;;;;;;AAIvB;AAEA,SAAS;IACP,uCAAmC;;IAA4B;IAC/D,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE;IACtE,OAAO,CAAC,iBAAiB,EAAE,QAAQ,GAAG,CAAC,IAAI,IAAI,MAAM;AACvD", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Development/protec/web/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,wRAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,+YAAC,wQAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}]}