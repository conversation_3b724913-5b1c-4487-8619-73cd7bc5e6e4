{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Development/protec/web/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Development/protec/web/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\"\nimport { PrismaAdapter } from \"@next-auth/prisma-adapter\"\nimport GoogleProvider from \"next-auth/providers/google\"\nimport GitHubProvider from \"next-auth/providers/github\"\nimport <PERSON>ailProvider from \"next-auth/providers/email\"\nimport { prisma } from \"./prisma\"\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n    GitHubProvider({\n      clientId: process.env.AUTH_GITHUB_ID!,\n      clientSecret: process.env.AUTH_GITHUB_SECRET!,\n    }),\n    EmailProvider({\n      server: {\n        host: process.env.EMAIL_SERVER_HOST,\n        port: Number(process.env.EMAIL_SERVER_PORT),\n        auth: {\n          user: process.env.EMAIL_SERVER_USER,\n          pass: process.env.EMAIL_SERVER_PASSWORD,\n        },\n        secure: process.env.EMAIL_SERVER_SECURE === 'true',\n      },\n      from: process.env.EMAIL_FROM,\n    }),\n  ],\n  session: {\n    strategy: \"jwt\",\n  },\n  callbacks: {\n    async jwt({ token, user, account }) {\n      if (user) {\n        token.id = user.id\n        token.email = user.email\n        token.name = user.name\n        token.picture = user.image\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id as string\n        session.user.email = token.email as string\n        session.user.name = token.name as string\n        session.user.image = token.picture as string\n      }\n      return session\n    },\n    async signIn({ user, account, profile }) {\n      if (account?.provider === \"google\" || account?.provider === \"github\") {\n        try {\n          // Check if alumni exists, if not create one\n          const existingAlumni = await prisma.alumni.findUnique({\n            where: { email: user.email! }\n          })\n\n          if (!existingAlumni) {\n            // Create new alumni record\n            await prisma.alumni.create({\n              data: {\n                email: user.email!,\n                name: user.name || \"\",\n                photoUrl: user.image,\n                graduationYear: new Date().getFullYear(), // Default, can be updated later\n                programmes: [],\n                skills: [],\n                province: \"\",\n                city: \"\",\n                privacy: {\n                  showEmail: false,\n                  showPhone: false,\n                  showLocation: true,\n                  showConnections: true,\n                },\n              }\n            })\n          }\n        } catch (error) {\n          console.error(\"Error creating alumni record:\", error)\n          return false\n        }\n      }\n      return true\n    },\n  },\n  pages: {\n    signIn: \"/auth/signin\",\n    signUp: \"/auth/signup\",\n    error: \"/auth/error\",\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,mXAAA,CAAA,gBAAa,AAAD,EAAE,+GAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0WAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,0WAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,cAAc;YACpC,cAAc,QAAQ,GAAG,CAAC,kBAAkB;QAC9C;QACA,CAAA,GAAA,yWAAA,CAAA,UAAa,AAAD,EAAE;YACZ,QAAQ;gBACN,MAAM,QAAQ,GAAG,CAAC,iBAAiB;gBACnC,MAAM,OAAO,QAAQ,GAAG,CAAC,iBAAiB;gBAC1C,MAAM;oBACJ,MAAM,QAAQ,GAAG,CAAC,iBAAiB;oBACnC,MAAM,QAAQ,GAAG,CAAC,qBAAqB;gBACzC;gBACA,QAAQ,QAAQ,GAAG,CAAC,mBAAmB,KAAK;YAC9C;YACA,MAAM,QAAQ,GAAG,CAAC,UAAU;QAC9B;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE;YAChC,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,OAAO,GAAG,KAAK,KAAK;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,OAAO;YACpC;YACA,OAAO;QACT;QACA,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,IAAI,SAAS,aAAa,YAAY,SAAS,aAAa,UAAU;gBACpE,IAAI;oBACF,4CAA4C;oBAC5C,MAAM,iBAAiB,MAAM,+GAAA,CAAA,SAAM,CAAC,MAAM,CAAC,UAAU,CAAC;wBACpD,OAAO;4BAAE,OAAO,KAAK,KAAK;wBAAE;oBAC9B;oBAEA,IAAI,CAAC,gBAAgB;wBACnB,2BAA2B;wBAC3B,MAAM,+GAAA,CAAA,SAAM,CAAC,MAAM,CAAC,MAAM,CAAC;4BACzB,MAAM;gCACJ,OAAO,KAAK,KAAK;gCACjB,MAAM,KAAK,IAAI,IAAI;gCACnB,UAAU,KAAK,KAAK;gCACpB,gBAAgB,IAAI,OAAO,WAAW;gCACtC,YAAY,EAAE;gCACd,QAAQ,EAAE;gCACV,UAAU;gCACV,MAAM;gCACN,SAAS;oCACP,WAAW;oCACX,WAAW;oCACX,cAAc;oCACd,iBAAiB;gCACnB;4BACF;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iCAAiC;oBAC/C,OAAO;gBACT;YACF;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/Development/protec/web/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from \"next-auth\"\nimport { authOptions } from \"@/lib/auth\"\n\nconst handler = NextAuth(authOptions)\n\nexport { handler as GET, handler as POST }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,4VAAA,CAAA,UAAQ,AAAD,EAAE,6GAAA,CAAA,cAAW", "debugId": null}}]}