@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

:root {
  /* PROTEC Brand Colors */
  --protec-navy: #3B4A6B;
  --protec-red: #E31E24;
  --protec-light-blue: #B8D4F0;
  --protec-gray: #F5F6F7;

  --background: #ffffff;
  --foreground: #3B4A6B;
  --sidebar: hsl(0 0% 98%);
  --sidebar-foreground: #3B4A6B;
  --sidebar-primary: #3B4A6B;
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: #F5F6F7;
  --sidebar-accent-foreground: #3B4A6B;
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: #E31E24;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --protec-navy: #4A5A7A;
    --protec-red: #F03E44;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

.dark {
  --sidebar: hsl(240 5.9% 10%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: #F03E44;
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: #F03E44;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }
}

/* PROTEC specific utility classes */
.protec-gradient {
  background: linear-gradient(135deg, var(--protec-navy) 0%, var(--protec-red) 100%);
}

.text-protec-navy {
  color: var(--protec-navy);
}

.text-protec-red {
  color: var(--protec-red);
}

.bg-protec-navy {
  background-color: var(--protec-navy);
}

.bg-protec-red {
  background-color: var(--protec-red);
}

.bg-protec-gray {
  background-color: var(--protec-gray);
}

.border-protec-navy {
  border-color: var(--protec-navy);
}

.border-protec-red {
  border-color: var(--protec-red);
}

.hover\:bg-protec-navy:hover {
  background-color: var(--protec-navy);
}

.hover\:bg-protec-red:hover {
  background-color: var(--protec-red);
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}
