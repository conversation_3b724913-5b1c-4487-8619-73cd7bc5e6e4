@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import PROTEC Utilities */
@import '../styles/protec-utilities.css';

@custom-variant dark (&:is(.dark *));

:root {
    --background: oklch(1.0000 0 0);
    --foreground: oklch(0.3211 0 0);
    --card: oklch(1.0000 0 0);
    --card-foreground: oklch(0.3211 0 0);
    --popover: oklch(1.0000 0 0);
    --popover-foreground: oklch(0.3211 0 0);
    --primary: oklch(0.5824 0.2365 25.7065);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.9670 0.0029 264.5419);
    --secondary-foreground: oklch(0.4461 0.0263 256.8018);
    --muted: oklch(0.9846 0.0017 247.8389);
    --muted-foreground: oklch(0.5510 0.0234 264.3637);
    --accent: oklch(0.2933 0.0809 270.2148);
    --accent-foreground: oklch(1.0000 0 0);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.9276 0.0058 264.5313);
    --input: oklch(0.9276 0.0058 264.5313);
    --ring: oklch(0.5824 0.2365 25.7065);
    --chart-1: oklch(0.6231 0.1880 259.8145);
    --chart-2: oklch(0.5786 0.2242 26.6169);
    --chart-3: oklch(0.4882 0.2172 264.3763);
    --chart-4: oklch(0.4703 0.1698 21.4020);
    --chart-5: oklch(0.3791 0.1378 265.5222);
    --sidebar: oklch(0.9846 0.0017 247.8389);
    --sidebar-foreground: oklch(0.3211 0 0);
    --sidebar-primary: oklch(0.5824 0.2365 25.7065);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.2933 0.0809 270.2148);
    --sidebar-accent-foreground: oklch(1.0000 0 0);
    --sidebar-border: oklch(0.9276 0.0058 264.5313);
    --sidebar-ring: oklch(0.5824 0.2365 25.7065);
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.375rem;
    --shadow-2xs: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.06);
    --shadow-xs: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.06);
    --shadow-sm: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 1px 2px -1px hsl(0 0% 0% / 0.12);
    --shadow: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 1px 2px -1px hsl(0 0% 0% / 0.12);
    --shadow-md: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 2px 4px -1px hsl(0 0% 0% / 0.12);
    --shadow-lg: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 4px 6px -1px hsl(0 0% 0% / 0.12);
    --shadow-xl: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 8px 10px -1px hsl(0 0% 0% / 0.12);
    --shadow-2xl: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.30);
    --tracking-normal: 0.025em;
    --spacing: 0.25rem;
}

.dark {
    --background: oklch(0.2046 0 0);
    --foreground: oklch(0.9219 0 0);
    --card: oklch(0.2686 0 0);
    --card-foreground: oklch(0.9219 0 0);
    --popover: oklch(0.2686 0 0);
    --popover-foreground: oklch(0.9219 0 0);
    --primary: oklch(0.5824 0.2365 25.7065);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.2686 0 0);
    --secondary-foreground: oklch(0.9219 0 0);
    --muted: oklch(0.2686 0 0);
    --muted-foreground: oklch(0.7155 0 0);
    --accent: oklch(0.2933 0.0809 270.2148);
    --accent-foreground: oklch(1.0000 0 0);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.3715 0 0);
    --input: oklch(0.3715 0 0);
    --ring: oklch(0.5824 0.2365 25.7065);
    --chart-1: oklch(0.7137 0.1434 254.6240);
    --chart-2: oklch(0.6231 0.1880 259.8145);
    --chart-3: oklch(0.5461 0.2152 262.8809);
    --chart-4: oklch(0.4882 0.2172 264.3763);
    --chart-5: oklch(0.4244 0.1809 265.6377);
    --sidebar: oklch(0.2046 0 0);
    --sidebar-foreground: oklch(0.9219 0 0);
    --sidebar-primary: oklch(0.5824 0.2365 25.7065);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.2933 0.0809 270.2148);
    --sidebar-accent-foreground: oklch(0.8823 0.0571 254.1284);
    --sidebar-border: oklch(0.3715 0 0);
    --sidebar-ring: oklch(0.5824 0.2365 25.7065);
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.375rem;
    --shadow-2xs: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.06);
    --shadow-xs: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.06);
    --shadow-sm: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 1px 2px -1px hsl(0 0% 0% / 0.12);
    --shadow: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 1px 2px -1px hsl(0 0% 0% / 0.12);
    --shadow-md: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 2px 4px -1px hsl(0 0% 0% / 0.12);
    --shadow-lg: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 4px 6px -1px hsl(0 0% 0% / 0.12);
    --shadow-xl: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 8px 10px -1px hsl(0 0% 0% / 0.12);
    --shadow-2xl: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.30);
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);

    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);

    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-normal: var(--tracking-normal);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
    letter-spacing: var(--tracking-normal);
}

@layer base {
    * {
        border-color: hsl(var(--border));
    }

    body {
        background-color: hsl(var(--background));
        color: hsl(var(--foreground));
        font-family: Inter, ui-sans-serif, system-ui, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-feature-settings: "rlig" 1, "calt" 1;
    }

    /* Improved focus styles */
    *:focus-visible {
        @apply outline-2 outline-offset-2 outline-ring;
    }

    /* Smooth scrolling */
    html {
        scroll-behavior: smooth;
    }

    /* Better text rendering */
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        @apply font-semibold tracking-tight;
    }

    /* Improved button and interactive element styles */
    button,
    [role="button"] {
        @apply cursor-pointer;
    }

    button:disabled,
    [role="button"]:disabled {
        @apply cursor-not-allowed opacity-50;
    }
}

@layer components {

    /* PROTEC Brand Utility Classes */
    .protec-gradient {
        background: linear-gradient(135deg, hsl(var(--protec-navy)) 0%, hsl(var(--protec-red)) 100%);
    }

    .protec-gradient-subtle {
        background: linear-gradient(135deg, hsl(var(--protec-navy) / 0.1) 0%, hsl(var(--protec-red) / 0.1) 100%);
    }

    /* Enhanced card styles */
    .card-hover {
        @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
    }

    /* Loading states */
    .loading-skeleton {
        @apply animate-pulse bg-muted rounded;
    }

    /* Focus ring for custom components */
    .focus-ring {
        @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
    }

    /* Text utilities */
    .text-balance {
        text-wrap: balance;
    }

    /* Container utilities */
    .container-narrow {
        @apply container max-w-4xl;
    }

    .container-wide {
        @apply container max-w-7xl;
    }
}

@layer utilities {

    /* Animation utilities */
    .animate-fade-in {
        animation: fadeIn 0.3s ease-out;
    }

    .animate-slide-in {
        animation: slideIn 0.3s ease-out;
    }

    .animate-bounce-gentle {
        animation: bounceGentle 2s infinite;
    }

    /* Spacing utilities */
    .space-y-0\.5> :not([hidden])~ :not([hidden]) {
        margin-top: 0.125rem;
    }

    /* Text utilities */
    .text-pretty {
        text-wrap: pretty;
    }

    /* Backdrop utilities */
    .backdrop-blur-xs {
        backdrop-filter: blur(2px);
    }
}

/* Custom Keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceGentle {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-5px);
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    * {
        color-adjust: exact;
    }
}