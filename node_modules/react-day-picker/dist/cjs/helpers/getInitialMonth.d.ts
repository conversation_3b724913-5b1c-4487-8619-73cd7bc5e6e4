import { type DateLib } from "../classes/DateLib.js";
import { type DayPickerProps } from "../types/props.js";
/**
 * Determines the initial month to display in the calendar based on the provided
 * props.
 *
 * This function calculates the starting month, considering constraints such as
 * `startMonth`, `endMonth`, and the number of months to display.
 *
 * @param props The DayPicker props, including navigation and date constraints.
 * @param dateLib The date library to use for date manipulation.
 * @returns The initial month to display.
 */
export declare function getInitialMonth(props: Pick<DayPickerProps, "fromYear" | "toYear" | "month" | "defaultMonth" | "today" | "numberOfMonths" | "timeZone">, navStart: Date | undefined, navEnd: Date | undefined, dateLib: DateLib): Date;
