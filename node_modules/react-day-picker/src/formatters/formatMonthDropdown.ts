import { defaultDateLib, type DateLib } from "../classes/DateLib.js";

/**
 * Formats the month for the dropdown option label.
 *
 * @defaultValue The localized full month name.
 * @param month The date representing the month.
 * @param dateLib The date library to use for formatting. Defaults to
 *   `defaultDateLib`.
 * @returns The formatted month name as a string.
 * @group Formatters
 * @see https://daypicker.dev/docs/translation#custom-formatters
 */
export function formatMonthDropdown(
  month: Date,
  dateLib: DateLib = defaultDateLib
): string {
  return dateLib.format(month, "LLLL");
}
