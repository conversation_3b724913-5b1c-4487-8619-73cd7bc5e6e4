import type { DateLib } from "../classes/DateLib.js";
import type { DayPickerProps } from "../types/index.js";

/**
 * Returns the next month the user can navigate to, based on the given options.
 *
 * The next month is not always the next calendar month:
 *
 * - If it is after the `calendarEndMonth`, it returns `undefined`.
 * - If paged navigation is enabled, it skips forward by the number of displayed
 *   months.
 *
 * @param firstDisplayedMonth The first month currently displayed in the
 *   calendar.
 * @param calendarEndMonth The latest month the user can navigate to.
 * @param options Navigation options, including `numberOfMonths` and
 *   `pagedNavigation`.
 * @param dateLib The date library to use for date manipulation.
 * @returns The next month, or `undefined` if navigation is not possible.
 */
export function getNextMonth(
  firstDisplayedMonth: Date,
  calendarEndMonth: Date | undefined,
  options: Pick<
    DayPickerProps,
    "numberOfMonths" | "pagedNavigation" | "disableNavigation"
  >,
  dateLib: DateLib
): Date | undefined {
  if (options.disableNavigation) {
    return undefined;
  }
  const { pagedNavigation, numberOfMonths = 1 } = options;
  const { startOfMonth, addMonths, differenceInCalendarMonths } = dateLib;
  const offset = pagedNavigation ? numberOfMonths : 1;
  const month = startOfMonth(firstDisplayedMonth);

  if (!calendarEndMonth) {
    return addMonths(month, offset);
  }

  const monthsDiff = differenceInCalendarMonths(
    calendarEndMonth,
    firstDisplayedMonth
  );

  if (monthsDiff < numberOfMonths) {
    return undefined;
  }

  return addMonths(month, offset);
}
