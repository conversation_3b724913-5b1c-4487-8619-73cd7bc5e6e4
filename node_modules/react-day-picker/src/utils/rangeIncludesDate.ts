import { defaultDateLib } from "../classes/index.js";
import type { DateRange } from "../types/index.js";

/**
 * Checks if a given date is within a specified date range.
 *
 * @since 9.0.0
 * @param range - The date range to check against.
 * @param date - The date to check.
 * @param excludeEnds - If `true`, the range's start and end dates are excluded.
 * @param dateLib - The date utility library instance.
 * @returns `true` if the date is within the range, otherwise `false`.
 * @group Utilities
 */
export function rangeIncludesDate(
  range: DateRange,
  date: Date,
  excludeEnds = false,
  dateLib = defaultDateLib
): boolean {
  let { from, to } = range;
  const { differenceInCalendarDays, isSameDay } = dateLib;
  if (from && to) {
    const isRangeInverted = differenceInCalendarDays(to, from) < 0;
    if (isRangeInverted) {
      [from, to] = [to, from];
    }
    const isInRange =
      differenceInCalendarDays(date, from) >= (excludeEnds ? 1 : 0) &&
      differenceInCalendarDays(to, date) >= (excludeEnds ? 1 : 0);
    return isInRange;
  }
  if (!excludeEnds && to) {
    return isSameDay(to, date);
  }
  if (!excludeEnds && from) {
    return isSameDay(from, date);
  }
  return false;
}

/**
 * @private
 * @deprecated Use {@link rangeIncludesDate} instead.
 */
export const isDateInRange = (range: DateRange, date: Date) =>
  rangeIncludesDate(range, date, false, defaultDateLib);
