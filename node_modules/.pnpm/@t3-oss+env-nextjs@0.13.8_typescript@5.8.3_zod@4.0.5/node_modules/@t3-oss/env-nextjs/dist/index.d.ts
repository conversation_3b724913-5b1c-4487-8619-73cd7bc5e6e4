import { CreateEnv, CreateSchemaOptions, DefaultCombinedSchema, ServerClientOptions, StandardSchemaDictionary, StandardSchemaV1, StrictOptions } from "@t3-oss/env-core";

//#region src/index.d.ts

declare const CLIENT_PREFIX: "NEXT_PUBLIC_";
type ClientPrefix = typeof CLIENT_PREFIX;
type Options<TServer extends StandardSchemaDictionary, TClient extends Record<`${ClientPrefix}${string}`, StandardSchemaV1>, TShared extends StandardSchemaDictionary, TExtends extends Array<Record<string, unknown>>, TFinalSchema extends StandardSchemaV1<{}, {}>> = Omit<StrictOptions<ClientPrefix, TServer, TClient, TShared, TExtends> & ServerClientOptions<ClientPrefix, TServer, TClient> & CreateSchemaOptions<TServer, TClient, TShared, TFinalSchema>, "runtimeEnvStrict" | "runtimeEnv" | "clientPrefix"> & ({
  /**
  * Manual destruction of `process.env`. Required for Next.js < 13.4.4.
  */
  runtimeEnv: StrictOptions<ClientPrefix, TServer, TClient, TShared, TExtends>["runtimeEnvStrict"];
  experimental__runtimeEnv?: never;
} | {
  runtimeEnv?: never;
  /**
  * Can be used for Next.js ^13.4.4 since they stopped static analysis of server side `process.env`.
  * Only client side `process.env` is statically analyzed and needs to be manually destructured.
  */
  experimental__runtimeEnv: Record<{ [TKey in keyof TClient]: TKey extends `${ClientPrefix}${string}` ? TKey : never }[keyof TClient] | { [TKey in keyof TShared]: TKey extends string ? TKey : never }[keyof TShared], string | boolean | number | undefined>;
});
/**
* Create a new environment variable schema.
*/
declare function createEnv<TServer extends StandardSchemaDictionary = NonNullable<unknown>, TClient extends Record<`${ClientPrefix}${string}`, StandardSchemaV1> = NonNullable<unknown>, TShared extends StandardSchemaDictionary = NonNullable<unknown>, const TExtends extends Array<Record<string, unknown>> = [], TFinalSchema extends StandardSchemaV1<{}, {}> = DefaultCombinedSchema<TServer, TClient, TShared>>(opts: Options<TServer, TClient, TShared, TExtends, TFinalSchema>): CreateEnv<TFinalSchema, TExtends>;

//#endregion
export { createEnv };