{"name": "workbox-sw", "version": "7.3.0", "license": "MIT", "author": "Google's Web DevRel Team and Google's Aurora Team", "description": "This module makes it easy to get started with the Workbox service worker libraries.", "repository": {"type": "git", "url": "git+https://github.com/googlechrome/workbox.git"}, "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw"], "workbox": {"browserNamespace": "workbox", "packageType": "sw", "prodOnly": true}, "main": "build/workbox-sw.js", "module": "index.mjs", "gitHead": "c77dceb54d4af1749db95316710d6430e82b0c48"}