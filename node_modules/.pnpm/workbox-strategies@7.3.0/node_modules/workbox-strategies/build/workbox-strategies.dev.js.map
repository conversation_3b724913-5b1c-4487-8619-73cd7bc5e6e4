{"version": 3, "file": "workbox-strategies.dev.js", "sources": ["../_version.js", "../StrategyHandler.js", "../Strategy.js", "../utils/messages.js", "../CacheFirst.js", "../CacheOnly.js", "../plugins/cacheOkAndOpaquePlugin.js", "../NetworkFirst.js", "../NetworkOnly.js", "../StaleWhileRevalidate.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:strategies:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheMatchIgnoreParams } from 'workbox-core/_private/cacheMatchIgnoreParams.js';\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { executeQuotaErrorCallbacks } from 'workbox-core/_private/executeQuotaErrorCallbacks.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { timeout } from 'workbox-core/_private/timeout.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\nfunction toRequest(input) {\n    return typeof input === 'string' ? new Request(input) : input;\n}\n/**\n * A class created every time a Strategy instance instance calls\n * {@link workbox-strategies.Strategy~handle} or\n * {@link workbox-strategies.Strategy~handleAll} that wraps all fetch and\n * cache actions around plugin callbacks and keeps track of when the strategy\n * is \"done\" (i.e. all added `event.waitUntil()` promises have resolved).\n *\n * @memberof workbox-strategies\n */\nclass StrategyHandler {\n    /**\n     * Creates a new instance associated with the passed strategy and event\n     * that's handling the request.\n     *\n     * The constructor also initializes the state that will be passed to each of\n     * the plugins handling this request.\n     *\n     * @param {workbox-strategies.Strategy} strategy\n     * @param {Object} options\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params] The return value from the\n     *     {@link workbox-routing~matchCallback} (if applicable).\n     */\n    constructor(strategy, options) {\n        this._cacheKeys = {};\n        /**\n         * The request the strategy is performing (passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * @name request\n         * @instance\n         * @type {Request}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * The event associated with this request.\n         * @name event\n         * @instance\n         * @type {ExtendableEvent}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * A `URL` instance of `request.url` (if passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * Note: the `url` param will be present if the strategy was invoked\n         * from a workbox `Route` object.\n         * @name url\n         * @instance\n         * @type {URL|undefined}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * A `param` value (if passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * Note: the `param` param will be present if the strategy was invoked\n         * from a workbox `Route` object and the\n         * {@link workbox-routing~matchCallback} returned\n         * a truthy value (it will be that value).\n         * @name params\n         * @instance\n         * @type {*|undefined}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(options.event, ExtendableEvent, {\n                moduleName: 'workbox-strategies',\n                className: 'StrategyHandler',\n                funcName: 'constructor',\n                paramName: 'options.event',\n            });\n        }\n        Object.assign(this, options);\n        this.event = options.event;\n        this._strategy = strategy;\n        this._handlerDeferred = new Deferred();\n        this._extendLifetimePromises = [];\n        // Copy the plugins list (since it's mutable on the strategy),\n        // so any mutations don't affect this handler instance.\n        this._plugins = [...strategy.plugins];\n        this._pluginStateMap = new Map();\n        for (const plugin of this._plugins) {\n            this._pluginStateMap.set(plugin, {});\n        }\n        this.event.waitUntil(this._handlerDeferred.promise);\n    }\n    /**\n     * Fetches a given request (and invokes any applicable plugin callback\n     * methods) using the `fetchOptions` (for non-navigation requests) and\n     * `plugins` defined on the `Strategy` object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - `requestWillFetch()`\n     * - `fetchDidSucceed()`\n     * - `fetchDidFail()`\n     *\n     * @param {Request|string} input The URL or request to fetch.\n     * @return {Promise<Response>}\n     */\n    async fetch(input) {\n        const { event } = this;\n        let request = toRequest(input);\n        if (request.mode === 'navigate' &&\n            event instanceof FetchEvent &&\n            event.preloadResponse) {\n            const possiblePreloadResponse = (await event.preloadResponse);\n            if (possiblePreloadResponse) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Using a preloaded navigation response for ` +\n                        `'${getFriendlyURL(request.url)}'`);\n                }\n                return possiblePreloadResponse;\n            }\n        }\n        // If there is a fetchDidFail plugin, we need to save a clone of the\n        // original request before it's either modified by a requestWillFetch\n        // plugin or before the original request's body is consumed via fetch().\n        const originalRequest = this.hasCallback('fetchDidFail')\n            ? request.clone()\n            : null;\n        try {\n            for (const cb of this.iterateCallbacks('requestWillFetch')) {\n                request = await cb({ request: request.clone(), event });\n            }\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                throw new WorkboxError('plugin-error-request-will-fetch', {\n                    thrownErrorMessage: err.message,\n                });\n            }\n        }\n        // The request can be altered by plugins with `requestWillFetch` making\n        // the original request (most likely from a `fetch` event) different\n        // from the Request we make. Pass both to `fetchDidFail` to aid debugging.\n        const pluginFilteredRequest = request.clone();\n        try {\n            let fetchResponse;\n            // See https://github.com/GoogleChrome/workbox/issues/1796\n            fetchResponse = await fetch(request, request.mode === 'navigate' ? undefined : this._strategy.fetchOptions);\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Network request for ` +\n                    `'${getFriendlyURL(request.url)}' returned a response with ` +\n                    `status '${fetchResponse.status}'.`);\n            }\n            for (const callback of this.iterateCallbacks('fetchDidSucceed')) {\n                fetchResponse = await callback({\n                    event,\n                    request: pluginFilteredRequest,\n                    response: fetchResponse,\n                });\n            }\n            return fetchResponse;\n        }\n        catch (error) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log(`Network request for ` +\n                    `'${getFriendlyURL(request.url)}' threw an error.`, error);\n            }\n            // `originalRequest` will only exist if a `fetchDidFail` callback\n            // is being used (see above).\n            if (originalRequest) {\n                await this.runCallbacks('fetchDidFail', {\n                    error: error,\n                    event,\n                    originalRequest: originalRequest.clone(),\n                    request: pluginFilteredRequest.clone(),\n                });\n            }\n            throw error;\n        }\n    }\n    /**\n     * Calls `this.fetch()` and (in the background) runs `this.cachePut()` on\n     * the response generated by `this.fetch()`.\n     *\n     * The call to `this.cachePut()` automatically invokes `this.waitUntil()`,\n     * so you do not have to manually call `waitUntil()` on the event.\n     *\n     * @param {Request|string} input The request or URL to fetch and cache.\n     * @return {Promise<Response>}\n     */\n    async fetchAndCachePut(input) {\n        const response = await this.fetch(input);\n        const responseClone = response.clone();\n        void this.waitUntil(this.cachePut(input, responseClone));\n        return response;\n    }\n    /**\n     * Matches a request from the cache (and invokes any applicable plugin\n     * callback methods) using the `cacheName`, `matchOptions`, and `plugins`\n     * defined on the strategy object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - cacheKeyWillBeUsed()\n     * - cachedResponseWillBeUsed()\n     *\n     * @param {Request|string} key The Request or URL to use as the cache key.\n     * @return {Promise<Response|undefined>} A matching response, if found.\n     */\n    async cacheMatch(key) {\n        const request = toRequest(key);\n        let cachedResponse;\n        const { cacheName, matchOptions } = this._strategy;\n        const effectiveRequest = await this.getCacheKey(request, 'read');\n        const multiMatchOptions = Object.assign(Object.assign({}, matchOptions), { cacheName });\n        cachedResponse = await caches.match(effectiveRequest, multiMatchOptions);\n        if (process.env.NODE_ENV !== 'production') {\n            if (cachedResponse) {\n                logger.debug(`Found a cached response in '${cacheName}'.`);\n            }\n            else {\n                logger.debug(`No cached response found in '${cacheName}'.`);\n            }\n        }\n        for (const callback of this.iterateCallbacks('cachedResponseWillBeUsed')) {\n            cachedResponse =\n                (await callback({\n                    cacheName,\n                    matchOptions,\n                    cachedResponse,\n                    request: effectiveRequest,\n                    event: this.event,\n                })) || undefined;\n        }\n        return cachedResponse;\n    }\n    /**\n     * Puts a request/response pair in the cache (and invokes any applicable\n     * plugin callback methods) using the `cacheName` and `plugins` defined on\n     * the strategy object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - cacheKeyWillBeUsed()\n     * - cacheWillUpdate()\n     * - cacheDidUpdate()\n     *\n     * @param {Request|string} key The request or URL to use as the cache key.\n     * @param {Response} response The response to cache.\n     * @return {Promise<boolean>} `false` if a cacheWillUpdate caused the response\n     * not be cached, and `true` otherwise.\n     */\n    async cachePut(key, response) {\n        const request = toRequest(key);\n        // Run in the next task to avoid blocking other cache reads.\n        // https://github.com/w3c/ServiceWorker/issues/1397\n        await timeout(0);\n        const effectiveRequest = await this.getCacheKey(request, 'write');\n        if (process.env.NODE_ENV !== 'production') {\n            if (effectiveRequest.method && effectiveRequest.method !== 'GET') {\n                throw new WorkboxError('attempt-to-cache-non-get-request', {\n                    url: getFriendlyURL(effectiveRequest.url),\n                    method: effectiveRequest.method,\n                });\n            }\n            // See https://github.com/GoogleChrome/workbox/issues/2818\n            const vary = response.headers.get('Vary');\n            if (vary) {\n                logger.debug(`The response for ${getFriendlyURL(effectiveRequest.url)} ` +\n                    `has a 'Vary: ${vary}' header. ` +\n                    `Consider setting the {ignoreVary: true} option on your strategy ` +\n                    `to ensure cache matching and deletion works as expected.`);\n            }\n        }\n        if (!response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error(`Cannot cache non-existent response for ` +\n                    `'${getFriendlyURL(effectiveRequest.url)}'.`);\n            }\n            throw new WorkboxError('cache-put-with-no-response', {\n                url: getFriendlyURL(effectiveRequest.url),\n            });\n        }\n        const responseToCache = await this._ensureResponseSafeToCache(response);\n        if (!responseToCache) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Response '${getFriendlyURL(effectiveRequest.url)}' ` +\n                    `will not be cached.`, responseToCache);\n            }\n            return false;\n        }\n        const { cacheName, matchOptions } = this._strategy;\n        const cache = await self.caches.open(cacheName);\n        const hasCacheUpdateCallback = this.hasCallback('cacheDidUpdate');\n        const oldResponse = hasCacheUpdateCallback\n            ? await cacheMatchIgnoreParams(\n            // TODO(philipwalton): the `__WB_REVISION__` param is a precaching\n            // feature. Consider into ways to only add this behavior if using\n            // precaching.\n            cache, effectiveRequest.clone(), ['__WB_REVISION__'], matchOptions)\n            : null;\n        if (process.env.NODE_ENV !== 'production') {\n            logger.debug(`Updating the '${cacheName}' cache with a new Response ` +\n                `for ${getFriendlyURL(effectiveRequest.url)}.`);\n        }\n        try {\n            await cache.put(effectiveRequest, hasCacheUpdateCallback ? responseToCache.clone() : responseToCache);\n        }\n        catch (error) {\n            if (error instanceof Error) {\n                // See https://developer.mozilla.org/en-US/docs/Web/API/DOMException#exception-QuotaExceededError\n                if (error.name === 'QuotaExceededError') {\n                    await executeQuotaErrorCallbacks();\n                }\n                throw error;\n            }\n        }\n        for (const callback of this.iterateCallbacks('cacheDidUpdate')) {\n            await callback({\n                cacheName,\n                oldResponse,\n                newResponse: responseToCache.clone(),\n                request: effectiveRequest,\n                event: this.event,\n            });\n        }\n        return true;\n    }\n    /**\n     * Checks the list of plugins for the `cacheKeyWillBeUsed` callback, and\n     * executes any of those callbacks found in sequence. The final `Request`\n     * object returned by the last plugin is treated as the cache key for cache\n     * reads and/or writes. If no `cacheKeyWillBeUsed` plugin callbacks have\n     * been registered, the passed request is returned unmodified\n     *\n     * @param {Request} request\n     * @param {string} mode\n     * @return {Promise<Request>}\n     */\n    async getCacheKey(request, mode) {\n        const key = `${request.url} | ${mode}`;\n        if (!this._cacheKeys[key]) {\n            let effectiveRequest = request;\n            for (const callback of this.iterateCallbacks('cacheKeyWillBeUsed')) {\n                effectiveRequest = toRequest(await callback({\n                    mode,\n                    request: effectiveRequest,\n                    event: this.event,\n                    // params has a type any can't change right now.\n                    params: this.params, // eslint-disable-line\n                }));\n            }\n            this._cacheKeys[key] = effectiveRequest;\n        }\n        return this._cacheKeys[key];\n    }\n    /**\n     * Returns true if the strategy has at least one plugin with the given\n     * callback.\n     *\n     * @param {string} name The name of the callback to check for.\n     * @return {boolean}\n     */\n    hasCallback(name) {\n        for (const plugin of this._strategy.plugins) {\n            if (name in plugin) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n     * Runs all plugin callbacks matching the given name, in order, passing the\n     * given param object (merged ith the current plugin state) as the only\n     * argument.\n     *\n     * Note: since this method runs all plugins, it's not suitable for cases\n     * where the return value of a callback needs to be applied prior to calling\n     * the next callback. See\n     * {@link workbox-strategies.StrategyHandler#iterateCallbacks}\n     * below for how to handle that case.\n     *\n     * @param {string} name The name of the callback to run within each plugin.\n     * @param {Object} param The object to pass as the first (and only) param\n     *     when executing each callback. This object will be merged with the\n     *     current plugin state prior to callback execution.\n     */\n    async runCallbacks(name, param) {\n        for (const callback of this.iterateCallbacks(name)) {\n            // TODO(philipwalton): not sure why `any` is needed. It seems like\n            // this should work with `as WorkboxPluginCallbackParam[C]`.\n            await callback(param);\n        }\n    }\n    /**\n     * Accepts a callback and returns an iterable of matching plugin callbacks,\n     * where each callback is wrapped with the current handler state (i.e. when\n     * you call each callback, whatever object parameter you pass it will\n     * be merged with the plugin's current state).\n     *\n     * @param {string} name The name fo the callback to run\n     * @return {Array<Function>}\n     */\n    *iterateCallbacks(name) {\n        for (const plugin of this._strategy.plugins) {\n            if (typeof plugin[name] === 'function') {\n                const state = this._pluginStateMap.get(plugin);\n                const statefulCallback = (param) => {\n                    const statefulParam = Object.assign(Object.assign({}, param), { state });\n                    // TODO(philipwalton): not sure why `any` is needed. It seems like\n                    // this should work with `as WorkboxPluginCallbackParam[C]`.\n                    return plugin[name](statefulParam);\n                };\n                yield statefulCallback;\n            }\n        }\n    }\n    /**\n     * Adds a promise to the\n     * [extend lifetime promises]{@link https://w3c.github.io/ServiceWorker/#extendableevent-extend-lifetime-promises}\n     * of the event event associated with the request being handled (usually a\n     * `FetchEvent`).\n     *\n     * Note: you can await\n     * {@link workbox-strategies.StrategyHandler~doneWaiting}\n     * to know when all added promises have settled.\n     *\n     * @param {Promise} promise A promise to add to the extend lifetime promises\n     *     of the event that triggered the request.\n     */\n    waitUntil(promise) {\n        this._extendLifetimePromises.push(promise);\n        return promise;\n    }\n    /**\n     * Returns a promise that resolves once all promises passed to\n     * {@link workbox-strategies.StrategyHandler~waitUntil}\n     * have settled.\n     *\n     * Note: any work done after `doneWaiting()` settles should be manually\n     * passed to an event's `waitUntil()` method (not this handler's\n     * `waitUntil()` method), otherwise the service worker thread my be killed\n     * prior to your work completing.\n     */\n    async doneWaiting() {\n        let promise;\n        while ((promise = this._extendLifetimePromises.shift())) {\n            await promise;\n        }\n    }\n    /**\n     * Stops running the strategy and immediately resolves any pending\n     * `waitUntil()` promises.\n     */\n    destroy() {\n        this._handlerDeferred.resolve(null);\n    }\n    /**\n     * This method will call cacheWillUpdate on the available plugins (or use\n     * status === 200) to determine if the Response is safe and valid to cache.\n     *\n     * @param {Request} options.request\n     * @param {Response} options.response\n     * @return {Promise<Response|undefined>}\n     *\n     * @private\n     */\n    async _ensureResponseSafeToCache(response) {\n        let responseToCache = response;\n        let pluginsUsed = false;\n        for (const callback of this.iterateCallbacks('cacheWillUpdate')) {\n            responseToCache =\n                (await callback({\n                    request: this.request,\n                    response: responseToCache,\n                    event: this.event,\n                })) || undefined;\n            pluginsUsed = true;\n            if (!responseToCache) {\n                break;\n            }\n        }\n        if (!pluginsUsed) {\n            if (responseToCache && responseToCache.status !== 200) {\n                responseToCache = undefined;\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (responseToCache) {\n                    if (responseToCache.status !== 200) {\n                        if (responseToCache.status === 0) {\n                            logger.warn(`The response for '${this.request.url}' ` +\n                                `is an opaque response. The caching strategy that you're ` +\n                                `using will not cache opaque responses by default.`);\n                        }\n                        else {\n                            logger.debug(`The response for '${this.request.url}' ` +\n                                `returned a status code of '${response.status}' and won't ` +\n                                `be cached as a result.`);\n                        }\n                    }\n                }\n            }\n        }\n        return responseToCache;\n    }\n}\nexport { StrategyHandler };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { StrategyHandler } from './StrategyHandler.js';\nimport './_version.js';\n/**\n * An abstract base class that all other strategy classes must extend from:\n *\n * @memberof workbox-strategies\n */\nclass Strategy {\n    /**\n     * Creates a new instance of the strategy and sets all documented option\n     * properties as public instance properties.\n     *\n     * Note: if a custom strategy class extends the base Strategy class and does\n     * not need more than these properties, it does not need to define its own\n     * constructor.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     */\n    constructor(options = {}) {\n        /**\n         * Cache name to store and retrieve\n         * requests. Defaults to the cache names provided by\n         * {@link workbox-core.cacheNames}.\n         *\n         * @type {string}\n         */\n        this.cacheName = cacheNames.getRuntimeName(options.cacheName);\n        /**\n         * The list\n         * [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n         * used by this strategy.\n         *\n         * @type {Array<Object>}\n         */\n        this.plugins = options.plugins || [];\n        /**\n         * Values passed along to the\n         * [`init`]{@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters}\n         * of all fetch() requests made by this strategy.\n         *\n         * @type {Object}\n         */\n        this.fetchOptions = options.fetchOptions;\n        /**\n         * The\n         * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n         * for any `cache.match()` or `cache.put()` calls made by this strategy.\n         *\n         * @type {Object}\n         */\n        this.matchOptions = options.matchOptions;\n    }\n    /**\n     * Perform a request strategy and returns a `Promise` that will resolve with\n     * a `Response`, invoking all relevant plugin callbacks.\n     *\n     * When a strategy instance is registered with a Workbox\n     * {@link workbox-routing.Route}, this method is automatically\n     * called when the route matches.\n     *\n     * Alternatively, this method can be used in a standalone `FetchEvent`\n     * listener by passing it to `event.respondWith()`.\n     *\n     * @param {FetchEvent|Object} options A `FetchEvent` or an object with the\n     *     properties listed below.\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     */\n    handle(options) {\n        const [responseDone] = this.handleAll(options);\n        return responseDone;\n    }\n    /**\n     * Similar to {@link workbox-strategies.Strategy~handle}, but\n     * instead of just returning a `Promise` that resolves to a `Response` it\n     * it will return an tuple of `[response, done]` promises, where the former\n     * (`response`) is equivalent to what `handle()` returns, and the latter is a\n     * Promise that will resolve once any promises that were added to\n     * `event.waitUntil()` as part of performing the strategy have completed.\n     *\n     * You can await the `done` promise to ensure any extra work performed by\n     * the strategy (usually caching responses) completes successfully.\n     *\n     * @param {FetchEvent|Object} options A `FetchEvent` or an object with the\n     *     properties listed below.\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     * @return {Array<Promise>} A tuple of [response, done]\n     *     promises that can be used to determine when the response resolves as\n     *     well as when the handler has completed all its work.\n     */\n    handleAll(options) {\n        // Allow for flexible options to be passed.\n        if (options instanceof FetchEvent) {\n            options = {\n                event: options,\n                request: options.request,\n            };\n        }\n        const event = options.event;\n        const request = typeof options.request === 'string'\n            ? new Request(options.request)\n            : options.request;\n        const params = 'params' in options ? options.params : undefined;\n        const handler = new StrategyHandler(this, { event, request, params });\n        const responseDone = this._getResponse(handler, request, event);\n        const handlerDone = this._awaitComplete(responseDone, handler, request, event);\n        // Return an array of promises, suitable for use with Promise.all().\n        return [responseDone, handlerDone];\n    }\n    async _getResponse(handler, request, event) {\n        await handler.runCallbacks('handlerWillStart', { event, request });\n        let response = undefined;\n        try {\n            response = await this._handle(request, handler);\n            // The \"official\" Strategy subclasses all throw this error automatically,\n            // but in case a third-party Strategy doesn't, ensure that we have a\n            // consistent failure when there's no response or an error response.\n            if (!response || response.type === 'error') {\n                throw new WorkboxError('no-response', { url: request.url });\n            }\n        }\n        catch (error) {\n            if (error instanceof Error) {\n                for (const callback of handler.iterateCallbacks('handlerDidError')) {\n                    response = await callback({ error, event, request });\n                    if (response) {\n                        break;\n                    }\n                }\n            }\n            if (!response) {\n                throw error;\n            }\n            else if (process.env.NODE_ENV !== 'production') {\n                logger.log(`While responding to '${getFriendlyURL(request.url)}', ` +\n                    `an ${error instanceof Error ? error.toString() : ''} error occurred. Using a fallback response provided by ` +\n                    `a handlerDidError plugin.`);\n            }\n        }\n        for (const callback of handler.iterateCallbacks('handlerWillRespond')) {\n            response = await callback({ event, request, response });\n        }\n        return response;\n    }\n    async _awaitComplete(responseDone, handler, request, event) {\n        let response;\n        let error;\n        try {\n            response = await responseDone;\n        }\n        catch (error) {\n            // Ignore errors, as response errors should be caught via the `response`\n            // promise above. The `done` promise will only throw for errors in\n            // promises passed to `handler.waitUntil()`.\n        }\n        try {\n            await handler.runCallbacks('handlerDidRespond', {\n                event,\n                request,\n                response,\n            });\n            await handler.doneWaiting();\n        }\n        catch (waitUntilError) {\n            if (waitUntilError instanceof Error) {\n                error = waitUntilError;\n            }\n        }\n        await handler.runCallbacks('handlerDidComplete', {\n            event,\n            request,\n            response,\n            error: error,\n        });\n        handler.destroy();\n        if (error) {\n            throw error;\n        }\n    }\n}\nexport { Strategy };\n/**\n * Classes extending the `Strategy` based class should implement this method,\n * and leverage the {@link workbox-strategies.StrategyHandler}\n * arg to perform all fetching and cache logic, which will ensure all relevant\n * cache, cache options, fetch options and plugins are used (per the current\n * strategy instance).\n *\n * @name _handle\n * @instance\n * @abstract\n * @function\n * @param {Request} request\n * @param {workbox-strategies.StrategyHandler} handler\n * @return {Promise<Response>}\n *\n * @memberof workbox-strategies.Strategy\n */\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport '../_version.js';\nexport const messages = {\n    strategyStart: (strategyName, request) => `Using ${strategyName} to respond to '${getFriendlyURL(request.url)}'`,\n    printFinalResponse: (response) => {\n        if (response) {\n            logger.groupCollapsed(`View the final response here.`);\n            logger.log(response || '[No response returned]');\n            logger.groupEnd();\n        }\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a [cache-first](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#cache-first-falling-back-to-network)\n * request strategy.\n *\n * A cache first strategy is useful for assets that have been revisioned,\n * such as URLs like `/styles/example.a8f5f1.css`, since they\n * can be cached for long periods of time.\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass CacheFirst extends Strategy {\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'makeRequest',\n                paramName: 'request',\n            });\n        }\n        let response = await handler.cacheMatch(request);\n        let error = undefined;\n        if (!response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`No response found in the '${this.cacheName}' cache. ` +\n                    `Will respond with a network request.`);\n            }\n            try {\n                response = await handler.fetchAndCachePut(request);\n            }\n            catch (err) {\n                if (err instanceof Error) {\n                    error = err;\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (response) {\n                    logs.push(`Got response from network.`);\n                }\n                else {\n                    logs.push(`Unable to get a response from the network.`);\n                }\n            }\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`Found a cached response in the '${this.cacheName}' cache.`);\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n}\nexport { CacheFirst };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a [cache-only](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#cache-only)\n * request strategy.\n *\n * This class is useful if you want to take advantage of any\n * [Workbox plugins](https://developer.chrome.com/docs/workbox/using-plugins/).\n *\n * If there is no cache match, this will throw a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass CacheOnly extends Strategy {\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'makeRequest',\n                paramName: 'request',\n            });\n        }\n        const response = await handler.cacheMatch(request);\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            if (response) {\n                logger.log(`Found a cached response in the '${this.cacheName}' ` + `cache.`);\n                messages.printFinalResponse(response);\n            }\n            else {\n                logger.log(`No response found in the '${this.cacheName}' cache.`);\n            }\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url });\n        }\n        return response;\n    }\n}\nexport { CacheOnly };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nexport const cacheOkAndOpaquePlugin = {\n    /**\n     * Returns a valid response (to allow caching) if the status is 200 (OK) or\n     * 0 (opaque).\n     *\n     * @param {Object} options\n     * @param {Response} options.response\n     * @return {Response|null}\n     *\n     * @private\n     */\n    cacheWillUpdate: async ({ response }) => {\n        if (response.status === 200 || response.status === 0) {\n            return response;\n        }\n        return null;\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { cacheOkAndOpaquePlugin } from './plugins/cacheOkAndOpaquePlugin.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [network first](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#network-first-falling-back-to-cache)\n * request strategy.\n *\n * By default, this strategy will cache responses with a 200 status code as\n * well as [opaque responses](https://developer.chrome.com/docs/workbox/caching-resources-during-runtime/#opaque-responses).\n * Opaque responses are are cross-origin requests where the response doesn't\n * support [CORS](https://enable-cors.org/).\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass NetworkFirst extends Strategy {\n    /**\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] [`CacheQueryOptions`](https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions)\n     * @param {number} [options.networkTimeoutSeconds] If set, any network requests\n     * that fail to respond within the timeout will fallback to the cache.\n     *\n     * This option can be used to combat\n     * \"[lie-fi]{@link https://developers.google.com/web/fundamentals/performance/poor-connectivity/#lie-fi}\"\n     * scenarios.\n     */\n    constructor(options = {}) {\n        super(options);\n        // If this instance contains no plugins with a 'cacheWillUpdate' callback,\n        // prepend the `cacheOkAndOpaquePlugin` plugin to the plugins list.\n        if (!this.plugins.some((p) => 'cacheWillUpdate' in p)) {\n            this.plugins.unshift(cacheOkAndOpaquePlugin);\n        }\n        this._networkTimeoutSeconds = options.networkTimeoutSeconds || 0;\n        if (process.env.NODE_ENV !== 'production') {\n            if (this._networkTimeoutSeconds) {\n                assert.isType(this._networkTimeoutSeconds, 'number', {\n                    moduleName: 'workbox-strategies',\n                    className: this.constructor.name,\n                    funcName: 'constructor',\n                    paramName: 'networkTimeoutSeconds',\n                });\n            }\n        }\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'handle',\n                paramName: 'makeRequest',\n            });\n        }\n        const promises = [];\n        let timeoutId;\n        if (this._networkTimeoutSeconds) {\n            const { id, promise } = this._getTimeoutPromise({ request, logs, handler });\n            timeoutId = id;\n            promises.push(promise);\n        }\n        const networkPromise = this._getNetworkPromise({\n            timeoutId,\n            request,\n            logs,\n            handler,\n        });\n        promises.push(networkPromise);\n        const response = await handler.waitUntil((async () => {\n            // Promise.race() will resolve as soon as the first promise resolves.\n            return ((await handler.waitUntil(Promise.race(promises))) ||\n                // If Promise.race() resolved with null, it might be due to a network\n                // timeout + a cache miss. If that were to happen, we'd rather wait until\n                // the networkPromise resolves instead of returning null.\n                // Note that it's fine to await an already-resolved promise, so we don't\n                // have to check to see if it's still \"in flight\".\n                (await networkPromise));\n        })());\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url });\n        }\n        return response;\n    }\n    /**\n     * @param {Object} options\n     * @param {Request} options.request\n     * @param {Array} options.logs A reference to the logs array\n     * @param {Event} options.event\n     * @return {Promise<Response>}\n     *\n     * @private\n     */\n    _getTimeoutPromise({ request, logs, handler, }) {\n        let timeoutId;\n        const timeoutPromise = new Promise((resolve) => {\n            const onNetworkTimeout = async () => {\n                if (process.env.NODE_ENV !== 'production') {\n                    logs.push(`Timing out the network response at ` +\n                        `${this._networkTimeoutSeconds} seconds.`);\n                }\n                resolve(await handler.cacheMatch(request));\n            };\n            timeoutId = setTimeout(onNetworkTimeout, this._networkTimeoutSeconds * 1000);\n        });\n        return {\n            promise: timeoutPromise,\n            id: timeoutId,\n        };\n    }\n    /**\n     * @param {Object} options\n     * @param {number|undefined} options.timeoutId\n     * @param {Request} options.request\n     * @param {Array} options.logs A reference to the logs Array.\n     * @param {Event} options.event\n     * @return {Promise<Response>}\n     *\n     * @private\n     */\n    async _getNetworkPromise({ timeoutId, request, logs, handler, }) {\n        let error;\n        let response;\n        try {\n            response = await handler.fetchAndCachePut(request);\n        }\n        catch (fetchError) {\n            if (fetchError instanceof Error) {\n                error = fetchError;\n            }\n        }\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (response) {\n                logs.push(`Got response from network.`);\n            }\n            else {\n                logs.push(`Unable to get a response from the network. Will respond ` +\n                    `with a cached response.`);\n            }\n        }\n        if (error || !response) {\n            response = await handler.cacheMatch(request);\n            if (process.env.NODE_ENV !== 'production') {\n                if (response) {\n                    logs.push(`Found a cached response in the '${this.cacheName}'` + ` cache.`);\n                }\n                else {\n                    logs.push(`No response found in the '${this.cacheName}' cache.`);\n                }\n            }\n        }\n        return response;\n    }\n}\nexport { NetworkFirst };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { timeout } from 'workbox-core/_private/timeout.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [network-only](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#network-only)\n * request strategy.\n *\n * This class is useful if you want to take advantage of any\n * [Workbox plugins](https://developer.chrome.com/docs/workbox/using-plugins/).\n *\n * If the network request fails, this will throw a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass NetworkOnly extends Strategy {\n    /**\n     * @param {Object} [options]\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {number} [options.networkTimeoutSeconds] If set, any network requests\n     * that fail to respond within the timeout will result in a network error.\n     */\n    constructor(options = {}) {\n        super(options);\n        this._networkTimeoutSeconds = options.networkTimeoutSeconds || 0;\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: '_handle',\n                paramName: 'request',\n            });\n        }\n        let error = undefined;\n        let response;\n        try {\n            const promises = [\n                handler.fetch(request),\n            ];\n            if (this._networkTimeoutSeconds) {\n                const timeoutPromise = timeout(this._networkTimeoutSeconds * 1000);\n                promises.push(timeoutPromise);\n            }\n            response = await Promise.race(promises);\n            if (!response) {\n                throw new Error(`Timed out the network response after ` +\n                    `${this._networkTimeoutSeconds} seconds.`);\n            }\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                error = err;\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            if (response) {\n                logger.log(`Got response from network.`);\n            }\n            else {\n                logger.log(`Unable to get a response from the network.`);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n}\nexport { NetworkOnly };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { cacheOkAndOpaquePlugin } from './plugins/cacheOkAndOpaquePlugin.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [stale-while-revalidate](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#stale-while-revalidate)\n * request strategy.\n *\n * Resources are requested from both the cache and the network in parallel.\n * The strategy will respond with the cached version if available, otherwise\n * wait for the network response. The cache is updated with the network response\n * with each successful request.\n *\n * By default, this strategy will cache responses with a 200 status code as\n * well as [opaque responses](https://developer.chrome.com/docs/workbox/caching-resources-during-runtime/#opaque-responses).\n * Opaque responses are cross-origin requests where the response doesn't\n * support [CORS](https://enable-cors.org/).\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass StaleWhileRevalidate extends Strategy {\n    /**\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] [`CacheQueryOptions`](https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions)\n     */\n    constructor(options = {}) {\n        super(options);\n        // If this instance contains no plugins with a 'cacheWillUpdate' callback,\n        // prepend the `cacheOkAndOpaquePlugin` plugin to the plugins list.\n        if (!this.plugins.some((p) => 'cacheWillUpdate' in p)) {\n            this.plugins.unshift(cacheOkAndOpaquePlugin);\n        }\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'handle',\n                paramName: 'request',\n            });\n        }\n        const fetchAndCachePromise = handler.fetchAndCachePut(request).catch(() => {\n            // Swallow this error because a 'no-response' error will be thrown in\n            // main handler return flow. This will be in the `waitUntil()` flow.\n        });\n        void handler.waitUntil(fetchAndCachePromise);\n        let response = await handler.cacheMatch(request);\n        let error;\n        if (response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`Found a cached response in the '${this.cacheName}'` +\n                    ` cache. Will update with the network response in the background.`);\n            }\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`No response found in the '${this.cacheName}' cache. ` +\n                    `Will wait for the network response.`);\n            }\n            try {\n                // NOTE(philipwalton): Really annoying that we have to type cast here.\n                // https://github.com/microsoft/TypeScript/issues/20006\n                response = (await fetchAndCachePromise);\n            }\n            catch (err) {\n                if (err instanceof Error) {\n                    error = err;\n                }\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n}\nexport { StaleWhileRevalidate };\n"], "names": ["self", "_", "e", "toRequest", "input", "Request", "StrategyHandler", "constructor", "strategy", "options", "_cacheKeys", "assert", "isInstance", "event", "ExtendableEvent", "moduleName", "className", "funcName", "paramName", "Object", "assign", "_strategy", "_handler<PERSON><PERSON><PERSON><PERSON>", "Deferred", "_extendLifetimePromises", "_plugins", "plugins", "_pluginStateMap", "Map", "plugin", "set", "waitUntil", "promise", "fetch", "request", "mode", "FetchEvent", "preloadResponse", "possiblePreloadResponse", "logger", "log", "getFriendlyURL", "url", "originalRequest", "<PERSON><PERSON><PERSON><PERSON>", "clone", "cb", "iterateCallbacks", "err", "Error", "WorkboxError", "thrownErrorMessage", "message", "pluginFilteredRequest", "fetchResponse", "undefined", "fetchOptions", "process", "debug", "status", "callback", "response", "error", "runCallbacks", "fetchAndCachePut", "responseClone", "cachePut", "cacheMatch", "key", "cachedResponse", "cacheName", "matchOptions", "effectiveRequest", "get<PERSON><PERSON><PERSON><PERSON>", "multiMatchOptions", "caches", "match", "timeout", "method", "vary", "headers", "get", "responseToCache", "_ensureResponseSafeToCache", "cache", "open", "hasCacheUpdateCallback", "oldResponse", "cacheMatchIgnoreParams", "put", "name", "executeQuotaErrorCallbacks", "newResponse", "params", "param", "state", "stateful<PERSON><PERSON><PERSON>", "statefulParam", "push", "doneWaiting", "shift", "destroy", "resolve", "pluginsUsed", "warn", "Strategy", "cacheNames", "getRuntimeName", "handle", "responseDone", "handleAll", "handler", "_getResponse", "handlerDone", "_awaitComplete", "_handle", "type", "toString", "waitUntilError", "messages", "strategyStart", "strategyName", "printFinalResponse", "groupCollapsed", "groupEnd", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "logs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheOkAndOpaquePlugin", "cacheWillUpdate", "NetworkFirst", "some", "p", "unshift", "_networkTimeoutSeconds", "networkTimeoutSeconds", "isType", "promises", "timeoutId", "id", "_getTimeoutPromise", "networkPromise", "_getNetworkPromise", "Promise", "race", "timeoutPromise", "onNetworkTimeout", "setTimeout", "fetchError", "clearTimeout", "NetworkOnly", "StaleWhileRevalidate", "fetchAndCachePromise", "catch"], "mappings": ";;;;IACA;IACA,IAAI;IACAA,EAAAA,IAAI,CAAC,0BAA0B,CAAC,IAAIC,CAAC,EAAE,CAAA;IAC3C,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAUA,SAASC,SAASA,CAACC,KAAK,EAAE;MACtB,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAG,IAAIC,OAAO,CAACD,KAAK,CAAC,GAAGA,KAAK,CAAA;IACjE,CAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAME,eAAe,CAAC;IAClB;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIC,EAAAA,WAAWA,CAACC,QAAQ,EAAEC,OAAO,EAAE;IAC3B,IAAA,IAAI,CAACC,UAAU,GAAG,EAAE,CAAA;IACpB;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACQ;IACR;IACA;IACA;IACA;IACA;IACA;IACQ;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACQ;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACQ,IAA2C;UACvCC,gBAAM,CAACC,UAAU,CAACH,OAAO,CAACI,KAAK,EAAEC,eAAe,EAAE;IAC9CC,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,iBAAiB;IAC5BC,QAAAA,QAAQ,EAAE,aAAa;IACvBC,QAAAA,SAAS,EAAE,eAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;IACAC,IAAAA,MAAM,CAACC,MAAM,CAAC,IAAI,EAAEX,OAAO,CAAC,CAAA;IAC5B,IAAA,IAAI,CAACI,KAAK,GAAGJ,OAAO,CAACI,KAAK,CAAA;QAC1B,IAAI,CAACQ,SAAS,GAAGb,QAAQ,CAAA;IACzB,IAAA,IAAI,CAACc,gBAAgB,GAAG,IAAIC,oBAAQ,EAAE,CAAA;QACtC,IAAI,CAACC,uBAAuB,GAAG,EAAE,CAAA;IACjC;IACA;QACA,IAAI,CAACC,QAAQ,GAAG,CAAC,GAAGjB,QAAQ,CAACkB,OAAO,CAAC,CAAA;IACrC,IAAA,IAAI,CAACC,eAAe,GAAG,IAAIC,GAAG,EAAE,CAAA;IAChC,IAAA,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACJ,QAAQ,EAAE;UAChC,IAAI,CAACE,eAAe,CAACG,GAAG,CAACD,MAAM,EAAE,EAAE,CAAC,CAAA;IACxC,KAAA;QACA,IAAI,CAAChB,KAAK,CAACkB,SAAS,CAAC,IAAI,CAACT,gBAAgB,CAACU,OAAO,CAAC,CAAA;IACvD,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAMC,KAAKA,CAAC7B,KAAK,EAAE;QACf,MAAM;IAAES,MAAAA,KAAAA;IAAM,KAAC,GAAG,IAAI,CAAA;IACtB,IAAA,IAAIqB,OAAO,GAAG/B,SAAS,CAACC,KAAK,CAAC,CAAA;IAC9B,IAAA,IAAI8B,OAAO,CAACC,IAAI,KAAK,UAAU,IAC3BtB,KAAK,YAAYuB,UAAU,IAC3BvB,KAAK,CAACwB,eAAe,EAAE;IACvB,MAAA,MAAMC,uBAAuB,GAAI,MAAMzB,KAAK,CAACwB,eAAgB,CAAA;IAC7D,MAAA,IAAIC,uBAAuB,EAAE;IACzB,QAA2C;IACvCC,UAAAA,gBAAM,CAACC,GAAG,CAAE,CAAA,0CAAA,CAA2C,GAClD,CAAA,CAAA,EAAGC,gCAAc,CAACP,OAAO,CAACQ,GAAG,CAAE,GAAE,CAAC,CAAA;IAC3C,SAAA;IACA,QAAA,OAAOJ,uBAAuB,CAAA;IAClC,OAAA;IACJ,KAAA;IACA;IACA;IACA;IACA,IAAA,MAAMK,eAAe,GAAG,IAAI,CAACC,WAAW,CAAC,cAAc,CAAC,GAClDV,OAAO,CAACW,KAAK,EAAE,GACf,IAAI,CAAA;QACV,IAAI;UACA,KAAK,MAAMC,EAAE,IAAI,IAAI,CAACC,gBAAgB,CAAC,kBAAkB,CAAC,EAAE;YACxDb,OAAO,GAAG,MAAMY,EAAE,CAAC;IAAEZ,UAAAA,OAAO,EAAEA,OAAO,CAACW,KAAK,EAAE;IAAEhC,UAAAA,KAAAA;IAAM,SAAC,CAAC,CAAA;IAC3D,OAAA;SACH,CACD,OAAOmC,GAAG,EAAE;UACR,IAAIA,GAAG,YAAYC,KAAK,EAAE;IACtB,QAAA,MAAM,IAAIC,4BAAY,CAAC,iCAAiC,EAAE;cACtDC,kBAAkB,EAAEH,GAAG,CAACI,OAAAA;IAC5B,SAAC,CAAC,CAAA;IACN,OAAA;IACJ,KAAA;IACA;IACA;IACA;IACA,IAAA,MAAMC,qBAAqB,GAAGnB,OAAO,CAACW,KAAK,EAAE,CAAA;QAC7C,IAAI;IACA,MAAA,IAAIS,aAAa,CAAA;IACjB;IACAA,MAAAA,aAAa,GAAG,MAAMrB,KAAK,CAACC,OAAO,EAAEA,OAAO,CAACC,IAAI,KAAK,UAAU,GAAGoB,SAAS,GAAG,IAAI,CAAClC,SAAS,CAACmC,YAAY,CAAC,CAAA;IAC3G,MAAA,IAAIC,KAAoB,KAAK,YAAY,EAAE;IACvClB,QAAAA,gBAAM,CAACmB,KAAK,CAAE,sBAAqB,GAC9B,CAAA,CAAA,EAAGjB,gCAAc,CAACP,OAAO,CAACQ,GAAG,CAAE,6BAA4B,GAC3D,CAAA,QAAA,EAAUY,aAAa,CAACK,MAAO,IAAG,CAAC,CAAA;IAC5C,OAAA;UACA,KAAK,MAAMC,QAAQ,IAAI,IAAI,CAACb,gBAAgB,CAAC,iBAAiB,CAAC,EAAE;YAC7DO,aAAa,GAAG,MAAMM,QAAQ,CAAC;cAC3B/C,KAAK;IACLqB,UAAAA,OAAO,EAAEmB,qBAAqB;IAC9BQ,UAAAA,QAAQ,EAAEP,aAAAA;IACd,SAAC,CAAC,CAAA;IACN,OAAA;IACA,MAAA,OAAOA,aAAa,CAAA;SACvB,CACD,OAAOQ,KAAK,EAAE;IACV,MAA2C;IACvCvB,QAAAA,gBAAM,CAACC,GAAG,CAAE,CAAA,oBAAA,CAAqB,GAC5B,CAAGC,CAAAA,EAAAA,gCAAc,CAACP,OAAO,CAACQ,GAAG,CAAE,CAAkB,iBAAA,CAAA,EAAEoB,KAAK,CAAC,CAAA;IAClE,OAAA;IACA;IACA;IACA,MAAA,IAAInB,eAAe,EAAE;IACjB,QAAA,MAAM,IAAI,CAACoB,YAAY,CAAC,cAAc,EAAE;IACpCD,UAAAA,KAAK,EAAEA,KAAK;cACZjD,KAAK;IACL8B,UAAAA,eAAe,EAAEA,eAAe,CAACE,KAAK,EAAE;IACxCX,UAAAA,OAAO,EAAEmB,qBAAqB,CAACR,KAAK,EAAC;IACzC,SAAC,CAAC,CAAA;IACN,OAAA;IACA,MAAA,MAAMiB,KAAK,CAAA;IACf,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAME,gBAAgBA,CAAC5D,KAAK,EAAE;QAC1B,MAAMyD,QAAQ,GAAG,MAAM,IAAI,CAAC5B,KAAK,CAAC7B,KAAK,CAAC,CAAA;IACxC,IAAA,MAAM6D,aAAa,GAAGJ,QAAQ,CAAChB,KAAK,EAAE,CAAA;IACtC,IAAA,KAAK,IAAI,CAACd,SAAS,CAAC,IAAI,CAACmC,QAAQ,CAAC9D,KAAK,EAAE6D,aAAa,CAAC,CAAC,CAAA;IACxD,IAAA,OAAOJ,QAAQ,CAAA;IACnB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAMM,UAAUA,CAACC,GAAG,EAAE;IAClB,IAAA,MAAMlC,OAAO,GAAG/B,SAAS,CAACiE,GAAG,CAAC,CAAA;IAC9B,IAAA,IAAIC,cAAc,CAAA;QAClB,MAAM;UAAEC,SAAS;IAAEC,MAAAA,YAAAA;SAAc,GAAG,IAAI,CAAClD,SAAS,CAAA;QAClD,MAAMmD,gBAAgB,GAAG,MAAM,IAAI,CAACC,WAAW,CAACvC,OAAO,EAAE,MAAM,CAAC,CAAA;IAChE,IAAA,MAAMwC,iBAAiB,GAAGvD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEmD,YAAY,CAAC,EAAE;IAAED,MAAAA,SAAAA;IAAU,KAAC,CAAC,CAAA;QACvFD,cAAc,GAAG,MAAMM,MAAM,CAACC,KAAK,CAACJ,gBAAgB,EAAEE,iBAAiB,CAAC,CAAA;IACxE,IAA2C;IACvC,MAAA,IAAIL,cAAc,EAAE;IAChB9B,QAAAA,gBAAM,CAACmB,KAAK,CAAE,CAA8BY,4BAAAA,EAAAA,SAAU,IAAG,CAAC,CAAA;IAC9D,OAAC,MACI;IACD/B,QAAAA,gBAAM,CAACmB,KAAK,CAAE,CAA+BY,6BAAAA,EAAAA,SAAU,IAAG,CAAC,CAAA;IAC/D,OAAA;IACJ,KAAA;QACA,KAAK,MAAMV,QAAQ,IAAI,IAAI,CAACb,gBAAgB,CAAC,0BAA0B,CAAC,EAAE;IACtEsB,MAAAA,cAAc,GACV,CAAC,MAAMT,QAAQ,CAAC;YACZU,SAAS;YACTC,YAAY;YACZF,cAAc;IACdnC,QAAAA,OAAO,EAAEsC,gBAAgB;YACzB3D,KAAK,EAAE,IAAI,CAACA,KAAAA;WACf,CAAC,KAAK0C,SAAS,CAAA;IACxB,KAAA;IACA,IAAA,OAAOc,cAAc,CAAA;IACzB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMH,QAAQA,CAACE,GAAG,EAAEP,QAAQ,EAAE;IAC1B,IAAA,MAAM3B,OAAO,GAAG/B,SAAS,CAACiE,GAAG,CAAC,CAAA;IAC9B;IACA;QACA,MAAMS,kBAAO,CAAC,CAAC,CAAC,CAAA;QAChB,MAAML,gBAAgB,GAAG,MAAM,IAAI,CAACC,WAAW,CAACvC,OAAO,EAAE,OAAO,CAAC,CAAA;IACjE,IAA2C;UACvC,IAAIsC,gBAAgB,CAACM,MAAM,IAAIN,gBAAgB,CAACM,MAAM,KAAK,KAAK,EAAE;IAC9D,QAAA,MAAM,IAAI5B,4BAAY,CAAC,kCAAkC,EAAE;IACvDR,UAAAA,GAAG,EAAED,gCAAc,CAAC+B,gBAAgB,CAAC9B,GAAG,CAAC;cACzCoC,MAAM,EAAEN,gBAAgB,CAACM,MAAAA;IAC7B,SAAC,CAAC,CAAA;IACN,OAAA;IACA;UACA,MAAMC,IAAI,GAAGlB,QAAQ,CAACmB,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC,CAAA;IACzC,MAAA,IAAIF,IAAI,EAAE;IACNxC,QAAAA,gBAAM,CAACmB,KAAK,CAAE,oBAAmBjB,gCAAc,CAAC+B,gBAAgB,CAAC9B,GAAG,CAAE,CAAE,CAAA,CAAA,GACnE,gBAAeqC,IAAK,CAAA,UAAA,CAAW,GAC/B,CAAiE,gEAAA,CAAA,GACjE,0DAAyD,CAAC,CAAA;IACnE,OAAA;IACJ,KAAA;QACA,IAAI,CAAClB,QAAQ,EAAE;IACX,MAA2C;IACvCtB,QAAAA,gBAAM,CAACuB,KAAK,CAAE,CAAA,uCAAA,CAAwC,GACjD,CAAA,CAAA,EAAGrB,gCAAc,CAAC+B,gBAAgB,CAAC9B,GAAG,CAAE,IAAG,CAAC,CAAA;IACrD,OAAA;IACA,MAAA,MAAM,IAAIQ,4BAAY,CAAC,4BAA4B,EAAE;IACjDR,QAAAA,GAAG,EAAED,gCAAc,CAAC+B,gBAAgB,CAAC9B,GAAG,CAAA;IAC5C,OAAC,CAAC,CAAA;IACN,KAAA;QACA,MAAMwC,eAAe,GAAG,MAAM,IAAI,CAACC,0BAA0B,CAACtB,QAAQ,CAAC,CAAA;QACvE,IAAI,CAACqB,eAAe,EAAE;IAClB,MAA2C;IACvC3C,QAAAA,gBAAM,CAACmB,KAAK,CAAE,CAAA,UAAA,EAAYjB,gCAAc,CAAC+B,gBAAgB,CAAC9B,GAAG,CAAE,CAAG,EAAA,CAAA,GAC7D,CAAoB,mBAAA,CAAA,EAAEwC,eAAe,CAAC,CAAA;IAC/C,OAAA;IACA,MAAA,OAAO,KAAK,CAAA;IAChB,KAAA;QACA,MAAM;UAAEZ,SAAS;IAAEC,MAAAA,YAAAA;SAAc,GAAG,IAAI,CAAClD,SAAS,CAAA;QAClD,MAAM+D,KAAK,GAAG,MAAMpF,IAAI,CAAC2E,MAAM,CAACU,IAAI,CAACf,SAAS,CAAC,CAAA;IAC/C,IAAA,MAAMgB,sBAAsB,GAAG,IAAI,CAAC1C,WAAW,CAAC,gBAAgB,CAAC,CAAA;IACjE,IAAA,MAAM2C,WAAW,GAAGD,sBAAsB,GACpC,MAAME,gDAAsB;IAC9B;IACA;IACA;IACAJ,IAAAA,KAAK,EAAEZ,gBAAgB,CAAC3B,KAAK,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE0B,YAAY,CAAC,GACjE,IAAI,CAAA;IACV,IAA2C;IACvChC,MAAAA,gBAAM,CAACmB,KAAK,CAAE,CAAA,cAAA,EAAgBY,SAAU,CAA6B,4BAAA,CAAA,GAChE,CAAM7B,IAAAA,EAAAA,gCAAc,CAAC+B,gBAAgB,CAAC9B,GAAG,CAAE,GAAE,CAAC,CAAA;IACvD,KAAA;QACA,IAAI;IACA,MAAA,MAAM0C,KAAK,CAACK,GAAG,CAACjB,gBAAgB,EAAEc,sBAAsB,GAAGJ,eAAe,CAACrC,KAAK,EAAE,GAAGqC,eAAe,CAAC,CAAA;SACxG,CACD,OAAOpB,KAAK,EAAE;UACV,IAAIA,KAAK,YAAYb,KAAK,EAAE;IACxB;IACA,QAAA,IAAIa,KAAK,CAAC4B,IAAI,KAAK,oBAAoB,EAAE;cACrC,MAAMC,wDAA0B,EAAE,CAAA;IACtC,SAAA;IACA,QAAA,MAAM7B,KAAK,CAAA;IACf,OAAA;IACJ,KAAA;QACA,KAAK,MAAMF,QAAQ,IAAI,IAAI,CAACb,gBAAgB,CAAC,gBAAgB,CAAC,EAAE;IAC5D,MAAA,MAAMa,QAAQ,CAAC;YACXU,SAAS;YACTiB,WAAW;IACXK,QAAAA,WAAW,EAAEV,eAAe,CAACrC,KAAK,EAAE;IACpCX,QAAAA,OAAO,EAAEsC,gBAAgB;YACzB3D,KAAK,EAAE,IAAI,CAACA,KAAAA;IAChB,OAAC,CAAC,CAAA;IACN,KAAA;IACA,IAAA,OAAO,IAAI,CAAA;IACf,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAM4D,WAAWA,CAACvC,OAAO,EAAEC,IAAI,EAAE;QAC7B,MAAMiC,GAAG,GAAI,CAAElC,EAAAA,OAAO,CAACQ,GAAI,CAAA,GAAA,EAAKP,IAAK,CAAC,CAAA,CAAA;IACtC,IAAA,IAAI,CAAC,IAAI,CAACzB,UAAU,CAAC0D,GAAG,CAAC,EAAE;UACvB,IAAII,gBAAgB,GAAGtC,OAAO,CAAA;UAC9B,KAAK,MAAM0B,QAAQ,IAAI,IAAI,CAACb,gBAAgB,CAAC,oBAAoB,CAAC,EAAE;IAChEyB,QAAAA,gBAAgB,GAAGrE,SAAS,CAAC,MAAMyD,QAAQ,CAAC;cACxCzB,IAAI;IACJD,UAAAA,OAAO,EAAEsC,gBAAgB;cACzB3D,KAAK,EAAE,IAAI,CAACA,KAAK;IACjB;IACAgF,UAAAA,MAAM,EAAE,IAAI,CAACA,MAAM;IACvB,SAAC,CAAC,CAAC,CAAA;IACP,OAAA;IACA,MAAA,IAAI,CAACnF,UAAU,CAAC0D,GAAG,CAAC,GAAGI,gBAAgB,CAAA;IAC3C,KAAA;IACA,IAAA,OAAO,IAAI,CAAC9D,UAAU,CAAC0D,GAAG,CAAC,CAAA;IAC/B,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;MACIxB,WAAWA,CAAC8C,IAAI,EAAE;QACd,KAAK,MAAM7D,MAAM,IAAI,IAAI,CAACR,SAAS,CAACK,OAAO,EAAE;UACzC,IAAIgE,IAAI,IAAI7D,MAAM,EAAE;IAChB,QAAA,OAAO,IAAI,CAAA;IACf,OAAA;IACJ,KAAA;IACA,IAAA,OAAO,KAAK,CAAA;IAChB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMkC,YAAYA,CAAC2B,IAAI,EAAEI,KAAK,EAAE;QAC5B,KAAK,MAAMlC,QAAQ,IAAI,IAAI,CAACb,gBAAgB,CAAC2C,IAAI,CAAC,EAAE;IAChD;IACA;UACA,MAAM9B,QAAQ,CAACkC,KAAK,CAAC,CAAA;IACzB,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,CAAC/C,gBAAgBA,CAAC2C,IAAI,EAAE;QACpB,KAAK,MAAM7D,MAAM,IAAI,IAAI,CAACR,SAAS,CAACK,OAAO,EAAE;IACzC,MAAA,IAAI,OAAOG,MAAM,CAAC6D,IAAI,CAAC,KAAK,UAAU,EAAE;YACpC,MAAMK,KAAK,GAAG,IAAI,CAACpE,eAAe,CAACsD,GAAG,CAACpD,MAAM,CAAC,CAAA;YAC9C,MAAMmE,gBAAgB,GAAIF,KAAK,IAAK;IAChC,UAAA,MAAMG,aAAa,GAAG9E,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE0E,KAAK,CAAC,EAAE;IAAEC,YAAAA,KAAAA;IAAM,WAAC,CAAC,CAAA;IACxE;IACA;IACA,UAAA,OAAOlE,MAAM,CAAC6D,IAAI,CAAC,CAACO,aAAa,CAAC,CAAA;aACrC,CAAA;IACD,QAAA,MAAMD,gBAAgB,CAAA;IAC1B,OAAA;IACJ,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIjE,SAASA,CAACC,OAAO,EAAE;IACf,IAAA,IAAI,CAACR,uBAAuB,CAAC0E,IAAI,CAAClE,OAAO,CAAC,CAAA;IAC1C,IAAA,OAAOA,OAAO,CAAA;IAClB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAMmE,WAAWA,GAAG;IAChB,IAAA,IAAInE,OAAO,CAAA;QACX,OAAQA,OAAO,GAAG,IAAI,CAACR,uBAAuB,CAAC4E,KAAK,EAAE,EAAG;IACrD,MAAA,MAAMpE,OAAO,CAAA;IACjB,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACIqE,EAAAA,OAAOA,GAAG;IACN,IAAA,IAAI,CAAC/E,gBAAgB,CAACgF,OAAO,CAAC,IAAI,CAAC,CAAA;IACvC,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACI,MAAMnB,0BAA0BA,CAACtB,QAAQ,EAAE;QACvC,IAAIqB,eAAe,GAAGrB,QAAQ,CAAA;QAC9B,IAAI0C,WAAW,GAAG,KAAK,CAAA;QACvB,KAAK,MAAM3C,QAAQ,IAAI,IAAI,CAACb,gBAAgB,CAAC,iBAAiB,CAAC,EAAE;IAC7DmC,MAAAA,eAAe,GACX,CAAC,MAAMtB,QAAQ,CAAC;YACZ1B,OAAO,EAAE,IAAI,CAACA,OAAO;IACrB2B,QAAAA,QAAQ,EAAEqB,eAAe;YACzBrE,KAAK,EAAE,IAAI,CAACA,KAAAA;WACf,CAAC,KAAK0C,SAAS,CAAA;IACpBgD,MAAAA,WAAW,GAAG,IAAI,CAAA;UAClB,IAAI,CAACrB,eAAe,EAAE;IAClB,QAAA,MAAA;IACJ,OAAA;IACJ,KAAA;QACA,IAAI,CAACqB,WAAW,EAAE;IACd,MAAA,IAAIrB,eAAe,IAAIA,eAAe,CAACvB,MAAM,KAAK,GAAG,EAAE;IACnDuB,QAAAA,eAAe,GAAG3B,SAAS,CAAA;IAC/B,OAAA;IACA,MAA2C;IACvC,QAAA,IAAI2B,eAAe,EAAE;IACjB,UAAA,IAAIA,eAAe,CAACvB,MAAM,KAAK,GAAG,EAAE;IAChC,YAAA,IAAIuB,eAAe,CAACvB,MAAM,KAAK,CAAC,EAAE;IAC9BpB,cAAAA,gBAAM,CAACiE,IAAI,CAAE,CAAA,kBAAA,EAAoB,IAAI,CAACtE,OAAO,CAACQ,GAAI,CAAG,EAAA,CAAA,GAChD,CAAyD,wDAAA,CAAA,GACzD,mDAAkD,CAAC,CAAA;IAC5D,aAAC,MACI;IACDH,cAAAA,gBAAM,CAACmB,KAAK,CAAE,qBAAoB,IAAI,CAACxB,OAAO,CAACQ,GAAI,CAAG,EAAA,CAAA,GACjD,8BAA6BmB,QAAQ,CAACF,MAAO,CAAa,YAAA,CAAA,GAC1D,wBAAuB,CAAC,CAAA;IACjC,aAAA;IACJ,WAAA;IACJ,SAAA;IACJ,OAAA;IACJ,KAAA;IACA,IAAA,OAAOuB,eAAe,CAAA;IAC1B,GAAA;IACJ;;ICngBA;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA,MAAMuB,QAAQ,CAAC;IACX;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIlG,EAAAA,WAAWA,CAACE,OAAO,GAAG,EAAE,EAAE;IACtB;IACR;IACA;IACA;IACA;IACA;IACA;QACQ,IAAI,CAAC6D,SAAS,GAAGoC,wBAAU,CAACC,cAAc,CAAClG,OAAO,CAAC6D,SAAS,CAAC,CAAA;IAC7D;IACR;IACA;IACA;IACA;IACA;IACA;IACQ,IAAA,IAAI,CAAC5C,OAAO,GAAGjB,OAAO,CAACiB,OAAO,IAAI,EAAE,CAAA;IACpC;IACR;IACA;IACA;IACA;IACA;IACA;IACQ,IAAA,IAAI,CAAC8B,YAAY,GAAG/C,OAAO,CAAC+C,YAAY,CAAA;IACxC;IACR;IACA;IACA;IACA;IACA;IACA;IACQ,IAAA,IAAI,CAACe,YAAY,GAAG9D,OAAO,CAAC8D,YAAY,CAAA;IAC5C,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIqC,MAAMA,CAACnG,OAAO,EAAE;QACZ,MAAM,CAACoG,YAAY,CAAC,GAAG,IAAI,CAACC,SAAS,CAACrG,OAAO,CAAC,CAAA;IAC9C,IAAA,OAAOoG,YAAY,CAAA;IACvB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIC,SAASA,CAACrG,OAAO,EAAE;IACf;QACA,IAAIA,OAAO,YAAY2B,UAAU,EAAE;IAC/B3B,MAAAA,OAAO,GAAG;IACNI,QAAAA,KAAK,EAAEJ,OAAO;YACdyB,OAAO,EAAEzB,OAAO,CAACyB,OAAAA;WACpB,CAAA;IACL,KAAA;IACA,IAAA,MAAMrB,KAAK,GAAGJ,OAAO,CAACI,KAAK,CAAA;IAC3B,IAAA,MAAMqB,OAAO,GAAG,OAAOzB,OAAO,CAACyB,OAAO,KAAK,QAAQ,GAC7C,IAAI7B,OAAO,CAACI,OAAO,CAACyB,OAAO,CAAC,GAC5BzB,OAAO,CAACyB,OAAO,CAAA;QACrB,MAAM2D,MAAM,GAAG,QAAQ,IAAIpF,OAAO,GAAGA,OAAO,CAACoF,MAAM,GAAGtC,SAAS,CAAA;IAC/D,IAAA,MAAMwD,OAAO,GAAG,IAAIzG,eAAe,CAAC,IAAI,EAAE;UAAEO,KAAK;UAAEqB,OAAO;IAAE2D,MAAAA,MAAAA;IAAO,KAAC,CAAC,CAAA;QACrE,MAAMgB,YAAY,GAAG,IAAI,CAACG,YAAY,CAACD,OAAO,EAAE7E,OAAO,EAAErB,KAAK,CAAC,CAAA;IAC/D,IAAA,MAAMoG,WAAW,GAAG,IAAI,CAACC,cAAc,CAACL,YAAY,EAAEE,OAAO,EAAE7E,OAAO,EAAErB,KAAK,CAAC,CAAA;IAC9E;IACA,IAAA,OAAO,CAACgG,YAAY,EAAEI,WAAW,CAAC,CAAA;IACtC,GAAA;IACA,EAAA,MAAMD,YAAYA,CAACD,OAAO,EAAE7E,OAAO,EAAErB,KAAK,EAAE;IACxC,IAAA,MAAMkG,OAAO,CAAChD,YAAY,CAAC,kBAAkB,EAAE;UAAElD,KAAK;IAAEqB,MAAAA,OAAAA;IAAQ,KAAC,CAAC,CAAA;QAClE,IAAI2B,QAAQ,GAAGN,SAAS,CAAA;QACxB,IAAI;UACAM,QAAQ,GAAG,MAAM,IAAI,CAACsD,OAAO,CAACjF,OAAO,EAAE6E,OAAO,CAAC,CAAA;IAC/C;IACA;IACA;UACA,IAAI,CAAClD,QAAQ,IAAIA,QAAQ,CAACuD,IAAI,KAAK,OAAO,EAAE;IACxC,QAAA,MAAM,IAAIlE,4BAAY,CAAC,aAAa,EAAE;cAAER,GAAG,EAAER,OAAO,CAACQ,GAAAA;IAAI,SAAC,CAAC,CAAA;IAC/D,OAAA;SACH,CACD,OAAOoB,KAAK,EAAE;UACV,IAAIA,KAAK,YAAYb,KAAK,EAAE;YACxB,KAAK,MAAMW,QAAQ,IAAImD,OAAO,CAAChE,gBAAgB,CAAC,iBAAiB,CAAC,EAAE;cAChEc,QAAQ,GAAG,MAAMD,QAAQ,CAAC;gBAAEE,KAAK;gBAAEjD,KAAK;IAAEqB,YAAAA,OAAAA;IAAQ,WAAC,CAAC,CAAA;IACpD,UAAA,IAAI2B,QAAQ,EAAE;IACV,YAAA,MAAA;IACJ,WAAA;IACJ,SAAA;IACJ,OAAA;UACA,IAAI,CAACA,QAAQ,EAAE;IACX,QAAA,MAAMC,KAAK,CAAA;WACd,MAC+C;YAC5CvB,gBAAM,CAACC,GAAG,CAAE,CAAuBC,qBAAAA,EAAAA,gCAAc,CAACP,OAAO,CAACQ,GAAG,CAAE,CAAA,GAAA,CAAI,GAC9D,CAAA,GAAA,EAAKoB,KAAK,YAAYb,KAAK,GAAGa,KAAK,CAACuD,QAAQ,EAAE,GAAG,EAAG,CAAA,uDAAA,CAAwD,GAC5G,CAAA,yBAAA,CAA0B,CAAC,CAAA;IACpC,OAAA;IACJ,KAAA;QACA,KAAK,MAAMzD,QAAQ,IAAImD,OAAO,CAAChE,gBAAgB,CAAC,oBAAoB,CAAC,EAAE;UACnEc,QAAQ,GAAG,MAAMD,QAAQ,CAAC;YAAE/C,KAAK;YAAEqB,OAAO;IAAE2B,QAAAA,QAAAA;IAAS,OAAC,CAAC,CAAA;IAC3D,KAAA;IACA,IAAA,OAAOA,QAAQ,CAAA;IACnB,GAAA;MACA,MAAMqD,cAAcA,CAACL,YAAY,EAAEE,OAAO,EAAE7E,OAAO,EAAErB,KAAK,EAAE;IACxD,IAAA,IAAIgD,QAAQ,CAAA;IACZ,IAAA,IAAIC,KAAK,CAAA;QACT,IAAI;UACAD,QAAQ,GAAG,MAAMgD,YAAY,CAAA;SAChC,CACD,OAAO/C,KAAK,EAAE;IACV;IACA;IACA;IAAA,KAAA;QAEJ,IAAI;IACA,MAAA,MAAMiD,OAAO,CAAChD,YAAY,CAAC,mBAAmB,EAAE;YAC5ClD,KAAK;YACLqB,OAAO;IACP2B,QAAAA,QAAAA;IACJ,OAAC,CAAC,CAAA;IACF,MAAA,MAAMkD,OAAO,CAACZ,WAAW,EAAE,CAAA;SAC9B,CACD,OAAOmB,cAAc,EAAE;UACnB,IAAIA,cAAc,YAAYrE,KAAK,EAAE;IACjCa,QAAAA,KAAK,GAAGwD,cAAc,CAAA;IAC1B,OAAA;IACJ,KAAA;IACA,IAAA,MAAMP,OAAO,CAAChD,YAAY,CAAC,oBAAoB,EAAE;UAC7ClD,KAAK;UACLqB,OAAO;UACP2B,QAAQ;IACRC,MAAAA,KAAK,EAAEA,KAAAA;IACX,KAAC,CAAC,CAAA;QACFiD,OAAO,CAACV,OAAO,EAAE,CAAA;IACjB,IAAA,IAAIvC,KAAK,EAAE;IACP,MAAA,MAAMA,KAAK,CAAA;IACf,KAAA;IACJ,GAAA;IACJ,CAAA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;ICnOA;IACA;AACA;IACA;IACA;IACA;IACA;IAIO,MAAMyD,QAAQ,GAAG;IACpBC,EAAAA,aAAa,EAAEA,CAACC,YAAY,EAAEvF,OAAO,KAAM,CAAA,MAAA,EAAQuF,YAAa,CAAA,gBAAA,EAAkBhF,gCAAc,CAACP,OAAO,CAACQ,GAAG,CAAE,CAAE,CAAA,CAAA;MAChHgF,kBAAkB,EAAG7D,QAAQ,IAAK;IAC9B,IAAA,IAAIA,QAAQ,EAAE;IACVtB,MAAAA,gBAAM,CAACoF,cAAc,CAAE,CAAA,6BAAA,CAA8B,CAAC,CAAA;IACtDpF,MAAAA,gBAAM,CAACC,GAAG,CAACqB,QAAQ,IAAI,wBAAwB,CAAC,CAAA;UAChDtB,gBAAM,CAACqF,QAAQ,EAAE,CAAA;IACrB,KAAA;IACJ,GAAA;IACJ,CAAC;;ICnBD;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,UAAU,SAASpB,QAAQ,CAAC;IAC9B;IACJ;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMU,OAAOA,CAACjF,OAAO,EAAE6E,OAAO,EAAE;QAC5B,MAAMe,IAAI,GAAG,EAAE,CAAA;IACf,IAA2C;IACvCnH,MAAAA,gBAAM,CAACC,UAAU,CAACsB,OAAO,EAAE7B,OAAO,EAAE;IAChCU,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,IAAI,CAACT,WAAW,CAACmF,IAAI;IAChCzE,QAAAA,QAAQ,EAAE,aAAa;IACvBC,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,IAAI2C,QAAQ,GAAG,MAAMkD,OAAO,CAAC5C,UAAU,CAACjC,OAAO,CAAC,CAAA;QAChD,IAAI4B,KAAK,GAAGP,SAAS,CAAA;QACrB,IAAI,CAACM,QAAQ,EAAE;IACX,MAA2C;YACvCiE,IAAI,CAAC5B,IAAI,CAAE,CAA4B,0BAAA,EAAA,IAAI,CAAC5B,SAAU,CAAA,SAAA,CAAU,GAC3D,CAAA,oCAAA,CAAqC,CAAC,CAAA;IAC/C,OAAA;UACA,IAAI;IACAT,QAAAA,QAAQ,GAAG,MAAMkD,OAAO,CAAC/C,gBAAgB,CAAC9B,OAAO,CAAC,CAAA;WACrD,CACD,OAAOc,GAAG,EAAE;YACR,IAAIA,GAAG,YAAYC,KAAK,EAAE;IACtBa,UAAAA,KAAK,GAAGd,GAAG,CAAA;IACf,SAAA;IACJ,OAAA;IACA,MAA2C;IACvC,QAAA,IAAIa,QAAQ,EAAE;IACViE,UAAAA,IAAI,CAAC5B,IAAI,CAAE,CAAA,0BAAA,CAA2B,CAAC,CAAA;IAC3C,SAAC,MACI;IACD4B,UAAAA,IAAI,CAAC5B,IAAI,CAAE,CAAA,0CAAA,CAA2C,CAAC,CAAA;IAC3D,SAAA;IACJ,OAAA;IACJ,KAAC,MACI;IACD,MAA2C;YACvC4B,IAAI,CAAC5B,IAAI,CAAE,CAAA,gCAAA,EAAkC,IAAI,CAAC5B,SAAU,UAAS,CAAC,CAAA;IAC1E,OAAA;IACJ,KAAA;IACA,IAA2C;IACvC/B,MAAAA,gBAAM,CAACoF,cAAc,CAACJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAACjH,WAAW,CAACmF,IAAI,EAAExD,OAAO,CAAC,CAAC,CAAA;IAC7E,MAAA,KAAK,MAAMM,GAAG,IAAIsF,IAAI,EAAE;IACpBvF,QAAAA,gBAAM,CAACC,GAAG,CAACA,GAAG,CAAC,CAAA;IACnB,OAAA;IACA+E,MAAAA,QAAQ,CAACG,kBAAkB,CAAC7D,QAAQ,CAAC,CAAA;UACrCtB,gBAAM,CAACqF,QAAQ,EAAE,CAAA;IACrB,KAAA;QACA,IAAI,CAAC/D,QAAQ,EAAE;IACX,MAAA,MAAM,IAAIX,4BAAY,CAAC,aAAa,EAAE;YAAER,GAAG,EAAER,OAAO,CAACQ,GAAG;IAAEoB,QAAAA,KAAAA;IAAM,OAAC,CAAC,CAAA;IACtE,KAAA;IACA,IAAA,OAAOD,QAAQ,CAAA;IACnB,GAAA;IACJ;;ICvFA;IACA;AACA;IACA;IACA;IACA;IACA;IAOA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMkE,SAAS,SAAStB,QAAQ,CAAC;IAC7B;IACJ;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMU,OAAOA,CAACjF,OAAO,EAAE6E,OAAO,EAAE;IAC5B,IAA2C;IACvCpG,MAAAA,gBAAM,CAACC,UAAU,CAACsB,OAAO,EAAE7B,OAAO,EAAE;IAChCU,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,IAAI,CAACT,WAAW,CAACmF,IAAI;IAChCzE,QAAAA,QAAQ,EAAE,aAAa;IACvBC,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,MAAM2C,QAAQ,GAAG,MAAMkD,OAAO,CAAC5C,UAAU,CAACjC,OAAO,CAAC,CAAA;IAClD,IAA2C;IACvCK,MAAAA,gBAAM,CAACoF,cAAc,CAACJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAACjH,WAAW,CAACmF,IAAI,EAAExD,OAAO,CAAC,CAAC,CAAA;IAC7E,MAAA,IAAI2B,QAAQ,EAAE;YACVtB,gBAAM,CAACC,GAAG,CAAE,CAAkC,gCAAA,EAAA,IAAI,CAAC8B,SAAU,CAAA,EAAA,CAAG,GAAI,CAAA,MAAA,CAAO,CAAC,CAAA;IAC5EiD,QAAAA,QAAQ,CAACG,kBAAkB,CAAC7D,QAAQ,CAAC,CAAA;IACzC,OAAC,MACI;YACDtB,gBAAM,CAACC,GAAG,CAAE,CAAA,0BAAA,EAA4B,IAAI,CAAC8B,SAAU,UAAS,CAAC,CAAA;IACrE,OAAA;UACA/B,gBAAM,CAACqF,QAAQ,EAAE,CAAA;IACrB,KAAA;QACA,IAAI,CAAC/D,QAAQ,EAAE;IACX,MAAA,MAAM,IAAIX,4BAAY,CAAC,aAAa,EAAE;YAAER,GAAG,EAAER,OAAO,CAACQ,GAAAA;IAAI,OAAC,CAAC,CAAA;IAC/D,KAAA;IACA,IAAA,OAAOmB,QAAQ,CAAA;IACnB,GAAA;IACJ;;IC3DA;IACA;AACA;IACA;IACA;IACA;IACA;IAEO,MAAMmE,sBAAsB,GAAG;IAClC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACIC,eAAe,EAAE,OAAO;IAAEpE,IAAAA,QAAAA;IAAS,GAAC,KAAK;QACrC,IAAIA,QAAQ,CAACF,MAAM,KAAK,GAAG,IAAIE,QAAQ,CAACF,MAAM,KAAK,CAAC,EAAE;IAClD,MAAA,OAAOE,QAAQ,CAAA;IACnB,KAAA;IACA,IAAA,OAAO,IAAI,CAAA;IACf,GAAA;IACJ,CAAC;;ICzBD;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMqE,YAAY,SAASzB,QAAQ,CAAC;IAChC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIlG,EAAAA,WAAWA,CAACE,OAAO,GAAG,EAAE,EAAE;QACtB,KAAK,CAACA,OAAO,CAAC,CAAA;IACd;IACA;IACA,IAAA,IAAI,CAAC,IAAI,CAACiB,OAAO,CAACyG,IAAI,CAAEC,CAAC,IAAK,iBAAiB,IAAIA,CAAC,CAAC,EAAE;IACnD,MAAA,IAAI,CAAC1G,OAAO,CAAC2G,OAAO,CAACL,sBAAsB,CAAC,CAAA;IAChD,KAAA;IACA,IAAA,IAAI,CAACM,sBAAsB,GAAG7H,OAAO,CAAC8H,qBAAqB,IAAI,CAAC,CAAA;IAChE,IAA2C;UACvC,IAAI,IAAI,CAACD,sBAAsB,EAAE;YAC7B3H,gBAAM,CAAC6H,MAAM,CAAC,IAAI,CAACF,sBAAsB,EAAE,QAAQ,EAAE;IACjDvH,UAAAA,UAAU,EAAE,oBAAoB;IAChCC,UAAAA,SAAS,EAAE,IAAI,CAACT,WAAW,CAACmF,IAAI;IAChCzE,UAAAA,QAAQ,EAAE,aAAa;IACvBC,UAAAA,SAAS,EAAE,uBAAA;IACf,SAAC,CAAC,CAAA;IACN,OAAA;IACJ,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMiG,OAAOA,CAACjF,OAAO,EAAE6E,OAAO,EAAE;QAC5B,MAAMe,IAAI,GAAG,EAAE,CAAA;IACf,IAA2C;IACvCnH,MAAAA,gBAAM,CAACC,UAAU,CAACsB,OAAO,EAAE7B,OAAO,EAAE;IAChCU,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,IAAI,CAACT,WAAW,CAACmF,IAAI;IAChCzE,QAAAA,QAAQ,EAAE,QAAQ;IAClBC,QAAAA,SAAS,EAAE,aAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,MAAMuH,QAAQ,GAAG,EAAE,CAAA;IACnB,IAAA,IAAIC,SAAS,CAAA;QACb,IAAI,IAAI,CAACJ,sBAAsB,EAAE;UAC7B,MAAM;YAAEK,EAAE;IAAE3G,QAAAA,OAAAA;IAAQ,OAAC,GAAG,IAAI,CAAC4G,kBAAkB,CAAC;YAAE1G,OAAO;YAAE4F,IAAI;IAAEf,QAAAA,OAAAA;IAAQ,OAAC,CAAC,CAAA;IAC3E2B,MAAAA,SAAS,GAAGC,EAAE,CAAA;IACdF,MAAAA,QAAQ,CAACvC,IAAI,CAAClE,OAAO,CAAC,CAAA;IAC1B,KAAA;IACA,IAAA,MAAM6G,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAAC;UAC3CJ,SAAS;UACTxG,OAAO;UACP4F,IAAI;IACJf,MAAAA,OAAAA;IACJ,KAAC,CAAC,CAAA;IACF0B,IAAAA,QAAQ,CAACvC,IAAI,CAAC2C,cAAc,CAAC,CAAA;QAC7B,MAAMhF,QAAQ,GAAG,MAAMkD,OAAO,CAAChF,SAAS,CAAC,CAAC,YAAY;IAClD;IACA,MAAA,OAAQ,CAAC,MAAMgF,OAAO,CAAChF,SAAS,CAACgH,OAAO,CAACC,IAAI,CAACP,QAAQ,CAAC,CAAC;IACpD;IACA;IACA;IACA;IACA;IACC,MAAA,MAAMI,cAAc,CAAC,CAAA;SAC7B,GAAG,CAAC,CAAA;IACL,IAA2C;IACvCtG,MAAAA,gBAAM,CAACoF,cAAc,CAACJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAACjH,WAAW,CAACmF,IAAI,EAAExD,OAAO,CAAC,CAAC,CAAA;IAC7E,MAAA,KAAK,MAAMM,GAAG,IAAIsF,IAAI,EAAE;IACpBvF,QAAAA,gBAAM,CAACC,GAAG,CAACA,GAAG,CAAC,CAAA;IACnB,OAAA;IACA+E,MAAAA,QAAQ,CAACG,kBAAkB,CAAC7D,QAAQ,CAAC,CAAA;UACrCtB,gBAAM,CAACqF,QAAQ,EAAE,CAAA;IACrB,KAAA;QACA,IAAI,CAAC/D,QAAQ,EAAE;IACX,MAAA,MAAM,IAAIX,4BAAY,CAAC,aAAa,EAAE;YAAER,GAAG,EAAER,OAAO,CAACQ,GAAAA;IAAI,OAAC,CAAC,CAAA;IAC/D,KAAA;IACA,IAAA,OAAOmB,QAAQ,CAAA;IACnB,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI+E,EAAAA,kBAAkBA,CAAC;QAAE1G,OAAO;QAAE4F,IAAI;IAAEf,IAAAA,OAAAA;IAAS,GAAC,EAAE;IAC5C,IAAA,IAAI2B,SAAS,CAAA;IACb,IAAA,MAAMO,cAAc,GAAG,IAAIF,OAAO,CAAEzC,OAAO,IAAK;IAC5C,MAAA,MAAM4C,gBAAgB,GAAG,YAAY;IACjC,QAA2C;cACvCpB,IAAI,CAAC5B,IAAI,CAAE,CAAoC,mCAAA,CAAA,GAC1C,GAAE,IAAI,CAACoC,sBAAuB,CAAA,SAAA,CAAU,CAAC,CAAA;IAClD,SAAA;YACAhC,OAAO,CAAC,MAAMS,OAAO,CAAC5C,UAAU,CAACjC,OAAO,CAAC,CAAC,CAAA;WAC7C,CAAA;UACDwG,SAAS,GAAGS,UAAU,CAACD,gBAAgB,EAAE,IAAI,CAACZ,sBAAsB,GAAG,IAAI,CAAC,CAAA;IAChF,KAAC,CAAC,CAAA;QACF,OAAO;IACHtG,MAAAA,OAAO,EAAEiH,cAAc;IACvBN,MAAAA,EAAE,EAAED,SAAAA;SACP,CAAA;IACL,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMI,kBAAkBA,CAAC;QAAEJ,SAAS;QAAExG,OAAO;QAAE4F,IAAI;IAAEf,IAAAA,OAAAA;IAAS,GAAC,EAAE;IAC7D,IAAA,IAAIjD,KAAK,CAAA;IACT,IAAA,IAAID,QAAQ,CAAA;QACZ,IAAI;IACAA,MAAAA,QAAQ,GAAG,MAAMkD,OAAO,CAAC/C,gBAAgB,CAAC9B,OAAO,CAAC,CAAA;SACrD,CACD,OAAOkH,UAAU,EAAE;UACf,IAAIA,UAAU,YAAYnG,KAAK,EAAE;IAC7Ba,QAAAA,KAAK,GAAGsF,UAAU,CAAA;IACtB,OAAA;IACJ,KAAA;IACA,IAAA,IAAIV,SAAS,EAAE;UACXW,YAAY,CAACX,SAAS,CAAC,CAAA;IAC3B,KAAA;IACA,IAA2C;IACvC,MAAA,IAAI7E,QAAQ,EAAE;IACViE,QAAAA,IAAI,CAAC5B,IAAI,CAAE,CAAA,0BAAA,CAA2B,CAAC,CAAA;IAC3C,OAAC,MACI;IACD4B,QAAAA,IAAI,CAAC5B,IAAI,CAAE,CAAyD,wDAAA,CAAA,GAC/D,yBAAwB,CAAC,CAAA;IAClC,OAAA;IACJ,KAAA;IACA,IAAA,IAAIpC,KAAK,IAAI,CAACD,QAAQ,EAAE;IACpBA,MAAAA,QAAQ,GAAG,MAAMkD,OAAO,CAAC5C,UAAU,CAACjC,OAAO,CAAC,CAAA;IAC5C,MAA2C;IACvC,QAAA,IAAI2B,QAAQ,EAAE;cACViE,IAAI,CAAC5B,IAAI,CAAE,CAAkC,gCAAA,EAAA,IAAI,CAAC5B,SAAU,CAAA,CAAA,CAAE,GAAI,CAAA,OAAA,CAAQ,CAAC,CAAA;IAC/E,SAAC,MACI;cACDwD,IAAI,CAAC5B,IAAI,CAAE,CAAA,0BAAA,EAA4B,IAAI,CAAC5B,SAAU,UAAS,CAAC,CAAA;IACpE,SAAA;IACJ,OAAA;IACJ,KAAA;IACA,IAAA,OAAOT,QAAQ,CAAA;IACnB,GAAA;IACJ;;ICnMA;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMyF,WAAW,SAAS7C,QAAQ,CAAC;IAC/B;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIlG,EAAAA,WAAWA,CAACE,OAAO,GAAG,EAAE,EAAE;QACtB,KAAK,CAACA,OAAO,CAAC,CAAA;IACd,IAAA,IAAI,CAAC6H,sBAAsB,GAAG7H,OAAO,CAAC8H,qBAAqB,IAAI,CAAC,CAAA;IACpE,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMpB,OAAOA,CAACjF,OAAO,EAAE6E,OAAO,EAAE;IAC5B,IAA2C;IACvCpG,MAAAA,gBAAM,CAACC,UAAU,CAACsB,OAAO,EAAE7B,OAAO,EAAE;IAChCU,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,IAAI,CAACT,WAAW,CAACmF,IAAI;IAChCzE,QAAAA,QAAQ,EAAE,SAAS;IACnBC,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,IAAI4C,KAAK,GAAGP,SAAS,CAAA;IACrB,IAAA,IAAIM,QAAQ,CAAA;QACZ,IAAI;UACA,MAAM4E,QAAQ,GAAG,CACb1B,OAAO,CAAC9E,KAAK,CAACC,OAAO,CAAC,CACzB,CAAA;UACD,IAAI,IAAI,CAACoG,sBAAsB,EAAE;YAC7B,MAAMW,cAAc,GAAGpE,kBAAO,CAAC,IAAI,CAACyD,sBAAsB,GAAG,IAAI,CAAC,CAAA;IAClEG,QAAAA,QAAQ,CAACvC,IAAI,CAAC+C,cAAc,CAAC,CAAA;IACjC,OAAA;IACApF,MAAAA,QAAQ,GAAG,MAAMkF,OAAO,CAACC,IAAI,CAACP,QAAQ,CAAC,CAAA;UACvC,IAAI,CAAC5E,QAAQ,EAAE;YACX,MAAM,IAAIZ,KAAK,CAAE,CAAsC,qCAAA,CAAA,GAClD,GAAE,IAAI,CAACqF,sBAAuB,CAAA,SAAA,CAAU,CAAC,CAAA;IAClD,OAAA;SACH,CACD,OAAOtF,GAAG,EAAE;UACR,IAAIA,GAAG,YAAYC,KAAK,EAAE;IACtBa,QAAAA,KAAK,GAAGd,GAAG,CAAA;IACf,OAAA;IACJ,KAAA;IACA,IAA2C;IACvCT,MAAAA,gBAAM,CAACoF,cAAc,CAACJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAACjH,WAAW,CAACmF,IAAI,EAAExD,OAAO,CAAC,CAAC,CAAA;IAC7E,MAAA,IAAI2B,QAAQ,EAAE;IACVtB,QAAAA,gBAAM,CAACC,GAAG,CAAE,CAAA,0BAAA,CAA2B,CAAC,CAAA;IAC5C,OAAC,MACI;IACDD,QAAAA,gBAAM,CAACC,GAAG,CAAE,CAAA,0CAAA,CAA2C,CAAC,CAAA;IAC5D,OAAA;IACA+E,MAAAA,QAAQ,CAACG,kBAAkB,CAAC7D,QAAQ,CAAC,CAAA;UACrCtB,gBAAM,CAACqF,QAAQ,EAAE,CAAA;IACrB,KAAA;QACA,IAAI,CAAC/D,QAAQ,EAAE;IACX,MAAA,MAAM,IAAIX,4BAAY,CAAC,aAAa,EAAE;YAAER,GAAG,EAAER,OAAO,CAACQ,GAAG;IAAEoB,QAAAA,KAAAA;IAAM,OAAC,CAAC,CAAA;IACtE,KAAA;IACA,IAAA,OAAOD,QAAQ,CAAA;IACnB,GAAA;IACJ;;IChGA;IACA;AACA;IACA;IACA;IACA;IACA;IAQA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM0F,oBAAoB,SAAS9C,QAAQ,CAAC;IACxC;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACIlG,EAAAA,WAAWA,CAACE,OAAO,GAAG,EAAE,EAAE;QACtB,KAAK,CAACA,OAAO,CAAC,CAAA;IACd;IACA;IACA,IAAA,IAAI,CAAC,IAAI,CAACiB,OAAO,CAACyG,IAAI,CAAEC,CAAC,IAAK,iBAAiB,IAAIA,CAAC,CAAC,EAAE;IACnD,MAAA,IAAI,CAAC1G,OAAO,CAAC2G,OAAO,CAACL,sBAAsB,CAAC,CAAA;IAChD,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACI,EAAA,MAAMb,OAAOA,CAACjF,OAAO,EAAE6E,OAAO,EAAE;QAC5B,MAAMe,IAAI,GAAG,EAAE,CAAA;IACf,IAA2C;IACvCnH,MAAAA,gBAAM,CAACC,UAAU,CAACsB,OAAO,EAAE7B,OAAO,EAAE;IAChCU,QAAAA,UAAU,EAAE,oBAAoB;IAChCC,QAAAA,SAAS,EAAE,IAAI,CAACT,WAAW,CAACmF,IAAI;IAChCzE,QAAAA,QAAQ,EAAE,QAAQ;IAClBC,QAAAA,SAAS,EAAE,SAAA;IACf,OAAC,CAAC,CAAA;IACN,KAAA;QACA,MAAMsI,oBAAoB,GAAGzC,OAAO,CAAC/C,gBAAgB,CAAC9B,OAAO,CAAC,CAACuH,KAAK,CAAC,MAAM;IACvE;IACA;IAAA,KACH,CAAC,CAAA;IACF,IAAA,KAAK1C,OAAO,CAAChF,SAAS,CAACyH,oBAAoB,CAAC,CAAA;QAC5C,IAAI3F,QAAQ,GAAG,MAAMkD,OAAO,CAAC5C,UAAU,CAACjC,OAAO,CAAC,CAAA;IAChD,IAAA,IAAI4B,KAAK,CAAA;IACT,IAAA,IAAID,QAAQ,EAAE;IACV,MAA2C;YACvCiE,IAAI,CAAC5B,IAAI,CAAE,CAAkC,gCAAA,EAAA,IAAI,CAAC5B,SAAU,CAAA,CAAA,CAAE,GACzD,CAAA,gEAAA,CAAiE,CAAC,CAAA;IAC3E,OAAA;IACJ,KAAC,MACI;IACD,MAA2C;YACvCwD,IAAI,CAAC5B,IAAI,CAAE,CAA4B,0BAAA,EAAA,IAAI,CAAC5B,SAAU,CAAA,SAAA,CAAU,GAC3D,CAAA,mCAAA,CAAoC,CAAC,CAAA;IAC9C,OAAA;UACA,IAAI;IACA;IACA;YACAT,QAAQ,GAAI,MAAM2F,oBAAqB,CAAA;WAC1C,CACD,OAAOxG,GAAG,EAAE;YACR,IAAIA,GAAG,YAAYC,KAAK,EAAE;IACtBa,UAAAA,KAAK,GAAGd,GAAG,CAAA;IACf,SAAA;IACJ,OAAA;IACJ,KAAA;IACA,IAA2C;IACvCT,MAAAA,gBAAM,CAACoF,cAAc,CAACJ,QAAQ,CAACC,aAAa,CAAC,IAAI,CAACjH,WAAW,CAACmF,IAAI,EAAExD,OAAO,CAAC,CAAC,CAAA;IAC7E,MAAA,KAAK,MAAMM,GAAG,IAAIsF,IAAI,EAAE;IACpBvF,QAAAA,gBAAM,CAACC,GAAG,CAACA,GAAG,CAAC,CAAA;IACnB,OAAA;IACA+E,MAAAA,QAAQ,CAACG,kBAAkB,CAAC7D,QAAQ,CAAC,CAAA;UACrCtB,gBAAM,CAACqF,QAAQ,EAAE,CAAA;IACrB,KAAA;QACA,IAAI,CAAC/D,QAAQ,EAAE;IACX,MAAA,MAAM,IAAIX,4BAAY,CAAC,aAAa,EAAE;YAAER,GAAG,EAAER,OAAO,CAACQ,GAAG;IAAEoB,QAAAA,KAAAA;IAAM,OAAC,CAAC,CAAA;IACtE,KAAA;IACA,IAAA,OAAOD,QAAQ,CAAA;IACnB,GAAA;IACJ;;;;;;;;;;;;;;;;"}