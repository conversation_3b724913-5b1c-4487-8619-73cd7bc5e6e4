{"version": 3, "file": "hooks.module.js", "sources": ["../src/index.js"], "sourcesContent": ["import { options as _options } from 'preact';\n\n/** @type {number} */\nlet currentIndex;\n\n/** @type {import('./internal').Component} */\nlet currentComponent;\n\n/** @type {import('./internal').Component} */\nlet previousComponent;\n\n/** @type {number} */\nlet currentHook = 0;\n\n/** @type {Array<import('./internal').Component>} */\nlet afterPaintEffects = [];\n\n// Cast to use internal Options type\nconst options = /** @type {import('./internal').Options} */ (_options);\n\nlet oldBeforeDiff = options._diff;\nlet oldBeforeRender = options._render;\nlet oldAfterDiff = options.diffed;\nlet oldCommit = options._commit;\nlet oldBeforeUnmount = options.unmount;\nlet oldRoot = options._root;\n\nconst RAF_TIMEOUT = 100;\nlet prevRaf;\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions._diff = vnode => {\n\tcurrentComponent = null;\n\tif (oldBeforeDiff) oldBeforeDiff(vnode);\n};\n\noptions._root = (vnode, parentDom) => {\n\tif (vnode && parentDom._children && parentDom._children._mask) {\n\t\tvnode._mask = parentDom._children._mask;\n\t}\n\n\tif (oldRoot) oldRoot(vnode, parentDom);\n};\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions._render = vnode => {\n\tif (oldBeforeRender) oldBeforeRender(vnode);\n\n\tcurrentComponent = vnode._component;\n\tcurrentIndex = 0;\n\n\tconst hooks = currentComponent.__hooks;\n\tif (hooks) {\n\t\tif (previousComponent === currentComponent) {\n\t\t\thooks._pendingEffects = [];\n\t\t\tcurrentComponent._renderCallbacks = [];\n\t\t\thooks._list.forEach(hookItem => {\n\t\t\t\tif (hookItem._nextValue) {\n\t\t\t\t\thookItem._value = hookItem._nextValue;\n\t\t\t\t}\n\t\t\t\thookItem._pendingArgs = hookItem._nextValue = undefined;\n\t\t\t});\n\t\t} else {\n\t\t\thooks._pendingEffects.forEach(invokeCleanup);\n\t\t\thooks._pendingEffects.forEach(invokeEffect);\n\t\t\thooks._pendingEffects = [];\n\t\t\tcurrentIndex = 0;\n\t\t}\n\t}\n\tpreviousComponent = currentComponent;\n};\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.diffed = vnode => {\n\tif (oldAfterDiff) oldAfterDiff(vnode);\n\n\tconst c = vnode._component;\n\tif (c && c.__hooks) {\n\t\tif (c.__hooks._pendingEffects.length) afterPaint(afterPaintEffects.push(c));\n\t\tc.__hooks._list.forEach(hookItem => {\n\t\t\tif (hookItem._pendingArgs) {\n\t\t\t\thookItem._args = hookItem._pendingArgs;\n\t\t\t}\n\t\t\thookItem._pendingArgs = undefined;\n\t\t});\n\t}\n\tpreviousComponent = currentComponent = null;\n};\n\n// TODO: Improve typing of commitQueue parameter\n/** @type {(vnode: import('./internal').VNode, commitQueue: any) => void} */\noptions._commit = (vnode, commitQueue) => {\n\tcommitQueue.some(component => {\n\t\ttry {\n\t\t\tcomponent._renderCallbacks.forEach(invokeCleanup);\n\t\t\tcomponent._renderCallbacks = component._renderCallbacks.filter(cb =>\n\t\t\t\tcb._value ? invokeEffect(cb) : true\n\t\t\t);\n\t\t} catch (e) {\n\t\t\tcommitQueue.some(c => {\n\t\t\t\tif (c._renderCallbacks) c._renderCallbacks = [];\n\t\t\t});\n\t\t\tcommitQueue = [];\n\t\t\toptions._catchError(e, component._vnode);\n\t\t}\n\t});\n\n\tif (oldCommit) oldCommit(vnode, commitQueue);\n};\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.unmount = vnode => {\n\tif (oldBeforeUnmount) oldBeforeUnmount(vnode);\n\n\tconst c = vnode._component;\n\tif (c && c.__hooks) {\n\t\tlet hasErrored;\n\t\tc.__hooks._list.forEach(s => {\n\t\t\ttry {\n\t\t\t\tinvokeCleanup(s);\n\t\t\t} catch (e) {\n\t\t\t\thasErrored = e;\n\t\t\t}\n\t\t});\n\t\tc.__hooks = undefined;\n\t\tif (hasErrored) options._catchError(hasErrored, c._vnode);\n\t}\n};\n\n/**\n * Get a hook's state from the currentComponent\n * @param {number} index The index of the hook to get\n * @param {number} type The index of the hook to get\n * @returns {any}\n */\nfunction getHookState(index, type) {\n\tif (options._hook) {\n\t\toptions._hook(currentComponent, index, currentHook || type);\n\t}\n\tcurrentHook = 0;\n\n\t// Largely inspired by:\n\t// * https://github.com/michael-klein/funcy.js/blob/f6be73468e6ec46b0ff5aa3cc4c9baf72a29025a/src/hooks/core_hooks.mjs\n\t// * https://github.com/michael-klein/funcy.js/blob/650beaa58c43c33a74820a3c98b3c7079cf2e333/src/renderer.mjs\n\t// Other implementations to look at:\n\t// * https://codesandbox.io/s/mnox05qp8\n\tconst hooks =\n\t\tcurrentComponent.__hooks ||\n\t\t(currentComponent.__hooks = {\n\t\t\t_list: [],\n\t\t\t_pendingEffects: []\n\t\t});\n\n\tif (index >= hooks._list.length) {\n\t\thooks._list.push({});\n\t}\n\n\treturn hooks._list[index];\n}\n\n/**\n * @template {unknown} S\n * @param {import('./index').Dispatch<import('./index').StateUpdater<S>>} [initialState]\n * @returns {[S, (state: S) => void]}\n */\nexport function useState(initialState) {\n\tcurrentHook = 1;\n\treturn useReducer(invokeOrReturn, initialState);\n}\n\n/**\n * @template {unknown} S\n * @template {unknown} A\n * @param {import('./index').Reducer<S, A>} reducer\n * @param {import('./index').Dispatch<import('./index').StateUpdater<S>>} initialState\n * @param {(initialState: any) => void} [init]\n * @returns {[ S, (state: S) => void ]}\n */\nexport function useReducer(reducer, initialState, init) {\n\t/** @type {import('./internal').ReducerHookState} */\n\tconst hookState = getHookState(currentIndex++, 2);\n\thookState._reducer = reducer;\n\tif (!hookState._component) {\n\t\thookState._value = [\n\t\t\t!init ? invokeOrReturn(undefined, initialState) : init(initialState),\n\n\t\t\taction => {\n\t\t\t\tconst currentValue = hookState._nextValue\n\t\t\t\t\t? hookState._nextValue[0]\n\t\t\t\t\t: hookState._value[0];\n\t\t\t\tconst nextValue = hookState._reducer(currentValue, action);\n\n\t\t\t\tif (currentValue !== nextValue) {\n\t\t\t\t\thookState._nextValue = [nextValue, hookState._value[1]];\n\t\t\t\t\thookState._component.setState({});\n\t\t\t\t}\n\t\t\t}\n\t\t];\n\n\t\thookState._component = currentComponent;\n\n\t\tif (!currentComponent._hasScuFromHooks) {\n\t\t\tcurrentComponent._hasScuFromHooks = true;\n\t\t\tlet prevScu = currentComponent.shouldComponentUpdate;\n\t\t\tconst prevCWU = currentComponent.componentWillUpdate;\n\n\t\t\t// If we're dealing with a forced update `shouldComponentUpdate` will\n\t\t\t// not be called. But we use that to update the hook values, so we\n\t\t\t// need to call it.\n\t\t\tcurrentComponent.componentWillUpdate = function (p, s, c) {\n\t\t\t\tif (this._force) {\n\t\t\t\t\tlet tmp = prevScu;\n\t\t\t\t\t// Clear to avoid other sCU hooks from being called\n\t\t\t\t\tprevScu = undefined;\n\t\t\t\t\tupdateHookState(p, s, c);\n\t\t\t\t\tprevScu = tmp;\n\t\t\t\t}\n\n\t\t\t\tif (prevCWU) prevCWU.call(this, p, s, c);\n\t\t\t};\n\n\t\t\t// This SCU has the purpose of bailing out after repeated updates\n\t\t\t// to stateful hooks.\n\t\t\t// we store the next value in _nextValue[0] and keep doing that for all\n\t\t\t// state setters, if we have next states and\n\t\t\t// all next states within a component end up being equal to their original state\n\t\t\t// we are safe to bail out for this specific component.\n\t\t\t/**\n\t\t\t *\n\t\t\t * @type {import('./internal').Component[\"shouldComponentUpdate\"]}\n\t\t\t */\n\t\t\t// @ts-ignore - We don't use TS to downtranspile\n\t\t\t// eslint-disable-next-line no-inner-declarations\n\t\t\tfunction updateHookState(p, s, c) {\n\t\t\t\tif (!hookState._component.__hooks) return true;\n\n\t\t\t\t/** @type {(x: import('./internal').HookState) => x is import('./internal').ReducerHookState} */\n\t\t\t\tconst isStateHook = x => !!x._component;\n\t\t\t\tconst stateHooks =\n\t\t\t\t\thookState._component.__hooks._list.filter(isStateHook);\n\n\t\t\t\tconst allHooksEmpty = stateHooks.every(x => !x._nextValue);\n\t\t\t\t// When we have no updated hooks in the component we invoke the previous SCU or\n\t\t\t\t// traverse the VDOM tree further.\n\t\t\t\tif (allHooksEmpty) {\n\t\t\t\t\treturn prevScu ? prevScu.call(this, p, s, c) : true;\n\t\t\t\t}\n\n\t\t\t\t// We check whether we have components with a nextValue set that\n\t\t\t\t// have values that aren't equal to one another this pushes\n\t\t\t\t// us to update further down the tree\n\t\t\t\tlet shouldUpdate = false;\n\t\t\t\tstateHooks.forEach(hookItem => {\n\t\t\t\t\tif (hookItem._nextValue) {\n\t\t\t\t\t\tconst currentValue = hookItem._value[0];\n\t\t\t\t\t\thookItem._value = hookItem._nextValue;\n\t\t\t\t\t\thookItem._nextValue = undefined;\n\t\t\t\t\t\tif (currentValue !== hookItem._value[0]) shouldUpdate = true;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn shouldUpdate || hookState._component.props !== p\n\t\t\t\t\t? prevScu\n\t\t\t\t\t\t? prevScu.call(this, p, s, c)\n\t\t\t\t\t\t: true\n\t\t\t\t\t: false;\n\t\t\t}\n\n\t\t\tcurrentComponent.shouldComponentUpdate = updateHookState;\n\t\t}\n\t}\n\n\treturn hookState._nextValue || hookState._value;\n}\n\n/**\n * @param {import('./internal').Effect} callback\n * @param {unknown[]} args\n * @returns {void}\n */\nexport function useEffect(callback, args) {\n\t/** @type {import('./internal').EffectHookState} */\n\tconst state = getHookState(currentIndex++, 3);\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\n\t\tstate._value = callback;\n\t\tstate._pendingArgs = args;\n\n\t\tcurrentComponent.__hooks._pendingEffects.push(state);\n\t}\n}\n\n/**\n * @param {import('./internal').Effect} callback\n * @param {unknown[]} args\n * @returns {void}\n */\nexport function useLayoutEffect(callback, args) {\n\t/** @type {import('./internal').EffectHookState} */\n\tconst state = getHookState(currentIndex++, 4);\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\n\t\tstate._value = callback;\n\t\tstate._pendingArgs = args;\n\n\t\tcurrentComponent._renderCallbacks.push(state);\n\t}\n}\n\n/** @type {(initialValue: unknown) => unknown} */\nexport function useRef(initialValue) {\n\tcurrentHook = 5;\n\treturn useMemo(() => ({ current: initialValue }), []);\n}\n\n/**\n * @param {object} ref\n * @param {() => object} createHandle\n * @param {unknown[]} args\n * @returns {void}\n */\nexport function useImperativeHandle(ref, createHandle, args) {\n\tcurrentHook = 6;\n\tuseLayoutEffect(\n\t\t() => {\n\t\t\tif (typeof ref == 'function') {\n\t\t\t\tref(createHandle());\n\t\t\t\treturn () => ref(null);\n\t\t\t} else if (ref) {\n\t\t\t\tref.current = createHandle();\n\t\t\t\treturn () => (ref.current = null);\n\t\t\t}\n\t\t},\n\t\targs == null ? args : args.concat(ref)\n\t);\n}\n\n/**\n * @template {unknown} T\n * @param {() => T} factory\n * @param {unknown[]} args\n * @returns {T}\n */\nexport function useMemo(factory, args) {\n\t/** @type {import('./internal').MemoHookState<T>} */\n\tconst state = getHookState(currentIndex++, 7);\n\tif (argsChanged(state._args, args)) {\n\t\tstate._value = factory();\n\t\tstate._args = args;\n\t\tstate._factory = factory;\n\t}\n\n\treturn state._value;\n}\n\n/**\n * @param {() => void} callback\n * @param {unknown[]} args\n * @returns {() => void}\n */\nexport function useCallback(callback, args) {\n\tcurrentHook = 8;\n\treturn useMemo(() => callback, args);\n}\n\n/**\n * @param {import('./internal').PreactContext} context\n */\nexport function useContext(context) {\n\tconst provider = currentComponent.context[context._id];\n\t// We could skip this call here, but than we'd not call\n\t// `options._hook`. We need to do that in order to make\n\t// the devtools aware of this hook.\n\t/** @type {import('./internal').ContextHookState} */\n\tconst state = getHookState(currentIndex++, 9);\n\t// The devtools needs access to the context object to\n\t// be able to pull of the default value when no provider\n\t// is present in the tree.\n\tstate._context = context;\n\tif (!provider) return context._defaultValue;\n\t// This is probably not safe to convert to \"!\"\n\tif (state._value == null) {\n\t\tstate._value = true;\n\t\tprovider.sub(currentComponent);\n\t}\n\treturn provider.props.value;\n}\n\n/**\n * Display a custom label for a custom hook for the devtools panel\n * @type {<T>(value: T, cb?: (value: T) => string | number) => void}\n */\nexport function useDebugValue(value, formatter) {\n\tif (options.useDebugValue) {\n\t\toptions.useDebugValue(\n\t\t\tformatter ? formatter(value) : /** @type {any}*/ (value)\n\t\t);\n\t}\n}\n\n/**\n * @param {(error: unknown, errorInfo: import('preact').ErrorInfo) => void} cb\n * @returns {[unknown, () => void]}\n */\nexport function useErrorBoundary(cb) {\n\t/** @type {import('./internal').ErrorBoundaryHookState} */\n\tconst state = getHookState(currentIndex++, 10);\n\tconst errState = useState();\n\tstate._value = cb;\n\tif (!currentComponent.componentDidCatch) {\n\t\tcurrentComponent.componentDidCatch = (err, errorInfo) => {\n\t\t\tif (state._value) state._value(err, errorInfo);\n\t\t\terrState[1](err);\n\t\t};\n\t}\n\treturn [\n\t\terrState[0],\n\t\t() => {\n\t\t\terrState[1](undefined);\n\t\t}\n\t];\n}\n\n/** @type {() => string} */\nexport function useId() {\n\t/** @type {import('./internal').IdHookState} */\n\tconst state = getHookState(currentIndex++, 11);\n\tif (!state._value) {\n\t\t// Grab either the root node or the nearest async boundary node.\n\t\t/** @type {import('./internal.d').VNode} */\n\t\tlet root = currentComponent._vnode;\n\t\twhile (root !== null && !root._mask && root._parent !== null) {\n\t\t\troot = root._parent;\n\t\t}\n\n\t\tlet mask = root._mask || (root._mask = [0, 0]);\n\t\tstate._value = 'P' + mask[0] + '-' + mask[1]++;\n\t}\n\n\treturn state._value;\n}\n\n/**\n * After paint effects consumer.\n */\nfunction flushAfterPaintEffects() {\n\tlet component;\n\twhile ((component = afterPaintEffects.shift())) {\n\t\tif (!component._parentDom || !component.__hooks) continue;\n\t\ttry {\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeCleanup);\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeEffect);\n\t\t\tcomponent.__hooks._pendingEffects = [];\n\t\t} catch (e) {\n\t\t\tcomponent.__hooks._pendingEffects = [];\n\t\t\toptions._catchError(e, component._vnode);\n\t\t}\n\t}\n}\n\nlet HAS_RAF = typeof requestAnimationFrame == 'function';\n\n/**\n * Schedule a callback to be invoked after the browser has a chance to paint a new frame.\n * Do this by combining requestAnimationFrame (rAF) + setTimeout to invoke a callback after\n * the next browser frame.\n *\n * Also, schedule a timeout in parallel to the the rAF to ensure the callback is invoked\n * even if RAF doesn't fire (for example if the browser tab is not visible)\n *\n * @param {() => void} callback\n */\nfunction afterNextFrame(callback) {\n\tconst done = () => {\n\t\tclearTimeout(timeout);\n\t\tif (HAS_RAF) cancelAnimationFrame(raf);\n\t\tsetTimeout(callback);\n\t};\n\tconst timeout = setTimeout(done, RAF_TIMEOUT);\n\n\tlet raf;\n\tif (HAS_RAF) {\n\t\traf = requestAnimationFrame(done);\n\t}\n}\n\n// Note: if someone used options.debounceRendering = requestAnimationFrame,\n// then effects will ALWAYS run on the NEXT frame instead of the current one, incurring a ~16ms delay.\n// Perhaps this is not such a big deal.\n/**\n * Schedule afterPaintEffects flush after the browser paints\n * @param {number} newQueueLength\n * @returns {void}\n */\nfunction afterPaint(newQueueLength) {\n\tif (newQueueLength === 1 || prevRaf !== options.requestAnimationFrame) {\n\t\tprevRaf = options.requestAnimationFrame;\n\t\t(prevRaf || afterNextFrame)(flushAfterPaintEffects);\n\t}\n}\n\n/**\n * @param {import('./internal').HookState} hook\n * @returns {void}\n */\nfunction invokeCleanup(hook) {\n\t// A hook cleanup can introduce a call to render which creates a new root, this will call options.vnode\n\t// and move the currentComponent away.\n\tconst comp = currentComponent;\n\tlet cleanup = hook._cleanup;\n\tif (typeof cleanup == 'function') {\n\t\thook._cleanup = undefined;\n\t\tcleanup();\n\t}\n\n\tcurrentComponent = comp;\n}\n\n/**\n * Invoke a Hook's effect\n * @param {import('./internal').EffectHookState} hook\n * @returns {void}\n */\nfunction invokeEffect(hook) {\n\t// A hook call can introduce a call to render which creates a new root, this will call options.vnode\n\t// and move the currentComponent away.\n\tconst comp = currentComponent;\n\thook._cleanup = hook._value();\n\tcurrentComponent = comp;\n}\n\n/**\n * @param {unknown[]} oldArgs\n * @param {unknown[]} newArgs\n * @returns {boolean}\n */\nfunction argsChanged(oldArgs, newArgs) {\n\treturn (\n\t\t!oldArgs ||\n\t\toldArgs.length !== newArgs.length ||\n\t\tnewArgs.some((arg, index) => arg !== oldArgs[index])\n\t);\n}\n\n/**\n * @template Arg\n * @param {Arg} arg\n * @param {(arg: Arg) => any} f\n * @returns {any}\n */\nfunction invokeOrReturn(arg, f) {\n\treturn typeof f == 'function' ? f(arg) : f;\n}\n"], "names": ["currentIndex", "currentComponent", "previousComponent", "prevRaf", "currentHook", "afterPaintEffects", "options", "_options", "oldBeforeDiff", "__b", "oldBeforeRender", "__r", "oldAfterDiff", "diffed", "old<PERSON><PERSON><PERSON>", "__c", "oldBeforeUnmount", "unmount", "oldRoot", "__", "getHookState", "index", "type", "__h", "hooks", "__H", "length", "push", "useState", "initialState", "useReducer", "invokeOrReturn", "reducer", "init", "hookState", "_reducer", "undefined", "action", "currentValue", "__N", "nextValue", "setState", "_hasScuFromHooks", "updateHookState", "p", "s", "c", "stateHooks", "filter", "x", "every", "prevScu", "call", "this", "shouldUpdate", "for<PERSON>ach", "hookItem", "props", "shouldComponentUpdate", "prevCWU", "componentWillUpdate", "__e", "tmp", "useEffect", "callback", "args", "state", "__s", "args<PERSON><PERSON><PERSON>", "_pendingArgs", "useLayoutEffect", "useRef", "initialValue", "useMemo", "current", "useImperativeHandle", "ref", "createHandle", "concat", "factory", "useCallback", "useContext", "context", "provider", "sub", "value", "useDebugValue", "formatter", "useErrorBoundary", "cb", "errState", "componentDidCatch", "err", "errorInfo", "useId", "root", "__v", "__m", "mask", "flushAfterPaintEffects", "component", "shift", "__P", "invokeCleanup", "invokeEffect", "e", "vnode", "parentDom", "__k", "requestAnimationFrame", "afterNextFrame", "commitQueue", "some", "hasErrored", "HAS_RAF", "raf", "done", "clearTimeout", "timeout", "cancelAnimationFrame", "setTimeout", "hook", "comp", "cleanup", "oldArgs", "newArgs", "arg", "f"], "mappings": "iCAGA,IAAIA,EAGAC,EAGAC,EAmBAC,EAhBAC,EAAc,EAGdC,EAAoB,GAGlBC,EAAuDC,EAEzDC,EAAgBF,EAAOG,IACvBC,EAAkBJ,EAAOK,IACzBC,EAAeN,EAAQO,OACvBC,EAAYR,EAAOS,IACnBC,EAAmBV,EAAQW,QAC3BC,EAAUZ,EAAOa,GA8GrB,SAASC,EAAaC,EAAOC,GACxBhB,EAAOiB,KACVjB,EAAOiB,IAAOtB,EAAkBoB,EAAOjB,GAAekB,GAEvDlB,EAAc,EAOd,IAAMoB,EACLvB,EAAgBwB,MACfxB,EAAgBwB,IAAW,CAC3BN,GAAO,GACPI,IAAiB,KAOnB,OAJIF,GAASG,EAAKL,GAAOO,QACxBF,EAAKL,GAAOQ,KAAK,CAAE,GAGbH,EAAKL,GAAOE,EACpB,CAOO,SAASO,EAASC,GAExB,OADAzB,EAAc,EACP0B,EAAWC,EAAgBF,EACnC,CAUgB,SAAAC,EAAWE,EAASH,EAAcI,GAEjD,IAAMC,EAAYd,EAAapB,IAAgB,GAE/C,GADAkC,EAAUC,EAAWH,GAChBE,EAASnB,MACbmB,EAASf,GAAU,CACjBc,EAAiDA,EAAKJ,GAA/CE,OAAeK,EAAWP,GAElC,SAAAQ,GACC,IAAMC,EAAeJ,EAASK,IAC3BL,EAASK,IAAY,GACrBL,EAASf,GAAQ,GACdqB,EAAYN,EAAUC,EAASG,EAAcD,GAE/CC,IAAiBE,IACpBN,EAASK,IAAc,CAACC,EAAWN,EAASf,GAAQ,IACpDe,EAASnB,IAAY0B,SAAS,CAAE,GAElC,GAGDP,EAASnB,IAAcd,GAElBA,EAAiByC,GAAkB,CAgC9B,IAAAC,EAAT,SAAyBC,EAAGC,EAAGC,GAC9B,IAAKZ,EAASnB,IAAAU,IAAqB,OAAW,EAG9C,IACMsB,EACLb,EAASnB,IAAAU,IAAAN,GAA0B6B,OAFhB,SAAAC,GAAC,QAAMA,EAAClC,GAAW,GAOvC,GAHsBgC,EAAWG,MAAM,SAAAD,GAAC,OAAKA,EAACV,GAAW,GAIxD,OAAOY,GAAUA,EAAQC,KAAKC,KAAMT,EAAGC,EAAGC,GAM3C,IAAIQ,GAAe,EAUnB,OATAP,EAAWQ,QAAQ,SAAAC,GAClB,GAAIA,EAAQjB,IAAa,CACxB,IAAMD,EAAekB,EAAQrC,GAAQ,GACrCqC,EAAQrC,GAAUqC,EAAQjB,IAC1BiB,EAAQjB,SAAcH,EAClBE,IAAiBkB,EAAQrC,GAAQ,KAAImC,GAAe,EACzD,CACD,MAEOA,GAAgBpB,EAASnB,IAAY0C,QAAUb,MACnDO,GACCA,EAAQC,KAAKC,KAAMT,EAAGC,EAAGC,GAG9B,EAhEA7C,EAAiByC,GAAmB,EACpC,IAAIS,EAAUlD,EAAiByD,sBACzBC,EAAU1D,EAAiB2D,oBAKjC3D,EAAiB2D,oBAAsB,SAAUhB,EAAGC,EAAGC,GACtD,GAAIO,KAAIQ,IAAS,CAChB,IAAIC,EAAMX,EAEVA,OAAUf,EACVO,EAAgBC,EAAGC,EAAGC,GACtBK,EAAUW,CACX,CAEIH,GAASA,EAAQP,KAAKC,KAAMT,EAAGC,EAAGC,EACvC,EAiDA7C,EAAiByD,sBAAwBf,CAC1C,CAGD,OAAOT,EAASK,KAAeL,EAASf,EACzC,CAOgB,SAAA4C,EAAUC,EAAUC,GAEnC,IAAMC,EAAQ9C,EAAapB,IAAgB,IACtCM,EAAO6D,KAAiBC,EAAYF,EAAKzC,IAAQwC,KACrDC,EAAK/C,GAAU6C,EACfE,EAAMG,EAAeJ,EAErBhE,EAAgBwB,IAAAF,IAAyBI,KAAKuC,GAEhD,CAOO,SAASI,EAAgBN,EAAUC,GAEzC,IAAMC,EAAQ9C,EAAapB,IAAgB,IACtCM,EAAO6D,KAAiBC,EAAYF,EAAKzC,IAAQwC,KACrDC,EAAK/C,GAAU6C,EACfE,EAAMG,EAAeJ,EAErBhE,EAAgBsB,IAAkBI,KAAKuC,GAEzC,CAGO,SAASK,EAAOC,GAEtB,OADApE,EAAc,EACPqE,EAAQ,WAAO,MAAA,CAAEC,QAASF,EAAc,EAAG,GACnD,CAQgB,SAAAG,EAAoBC,EAAKC,EAAcZ,GACtD7D,EAAc,EACdkE,EACC,WACC,MAAkB,mBAAPM,GACVA,EAAIC,KACS,WAAA,OAAAD,EAAI,KAAK,GACZA,GACVA,EAAIF,QAAUG,IACA,WAAA,OAAAD,EAAIF,QAAU,IAAI,QAF1B,CAIR,EACQ,MAART,EAAeA,EAAOA,EAAKa,OAAOF,GAEpC,CAQgB,SAAAH,EAAQM,EAASd,GAEhC,IAAMC,EAAQ9C,EAAapB,IAAgB,GAO3C,OANIoE,EAAYF,EAAKzC,IAAQwC,KAC5BC,EAAK/C,GAAU4D,IACfb,EAAKzC,IAASwC,EACdC,EAAK3C,IAAYwD,GAGXb,EAAK/C,EACb,CAOO,SAAS6D,EAAYhB,EAAUC,GAErC,OADA7D,EAAc,EACPqE,EAAQ,WAAA,OAAMT,CAAQ,EAAEC,EAChC,CAKO,SAASgB,EAAWC,GAC1B,IAAMC,EAAWlF,EAAiBiF,QAAQA,EAAOnE,KAK3CmD,EAAQ9C,EAAapB,IAAgB,GAK3C,OADAkE,EAAKpB,EAAYoC,EACZC,GAEe,MAAhBjB,EAAK/C,KACR+C,EAAK/C,IAAU,EACfgE,EAASC,IAAInF,IAEPkF,EAAS1B,MAAM4B,OANAH,EAAO/D,EAO9B,CAMO,SAASmE,EAAcD,EAAOE,GAChCjF,EAAQgF,eACXhF,EAAQgF,cACPC,EAAYA,EAAUF,GAAM,EAG/B,CAMO,SAASG,EAAiBC,GAEhC,IAAMvB,EAAQ9C,EAAapB,IAAgB,IACrC0F,EAAW9D,IAQjB,OAPAsC,EAAK/C,GAAUsE,EACVxF,EAAiB0F,oBACrB1F,EAAiB0F,kBAAoB,SAACC,EAAKC,GACtC3B,EAAK/C,IAAS+C,EAAK/C,GAAQyE,EAAKC,GACpCH,EAAS,GAAGE,EACb,GAEM,CACNF,EAAS,GACT,WACCA,EAAS,QAAGtD,EACb,EAEF,CAGO,SAAS0D,IAEf,IAAM5B,EAAQ9C,EAAapB,IAAgB,IAC3C,IAAKkE,EAAK/C,GAAS,CAIlB,IADA,IAAI4E,EAAO9F,EAAgB+F,IACX,OAATD,IAAkBA,EAAIE,KAA2B,OAAjBF,EAAI5E,IAC1C4E,EAAOA,EAAI5E,GAGZ,IAAI+E,EAAOH,EAAIE,MAAWF,EAAIE,IAAS,CAAC,EAAG,IAC3C/B,EAAK/C,GAAU,IAAM+E,EAAK,GAAK,IAAMA,EAAK,IAC3C,CAEA,OAAOhC,EAAK/C,EACb,CAKA,SAASgF,IAER,IADA,IAAIC,EACIA,EAAY/F,EAAkBgG,SACrC,GAAKD,EAASE,KAAgBF,EAAS3E,IACvC,IACC2E,EAAS3E,IAAAF,IAAyBgC,QAAQgD,GAC1CH,EAAS3E,IAAAF,IAAyBgC,QAAQiD,GAC1CJ,EAAS3E,IAAAF,IAA2B,EAIrC,CAHE,MAAOkF,GACRL,EAAS3E,IAAAF,IAA2B,GACpCjB,EAAOuD,IAAa4C,EAAGL,EAASJ,IACjC,CAEF,CAzaA1F,EAAOG,IAAS,SAAAiG,GACfzG,EAAmB,KACfO,GAAeA,EAAckG,EAClC,EAEApG,EAAOa,GAAS,SAACuF,EAAOC,GACnBD,GAASC,EAASC,KAAcD,EAASC,IAAAX,MAC5CS,EAAKT,IAASU,EAASC,IAAAX,KAGpB/E,GAASA,EAAQwF,EAAOC,EAC7B,EAGArG,EAAOK,IAAW,SAAA+F,GACbhG,GAAiBA,EAAgBgG,GAGrC1G,EAAe,EAEf,IAAMwB,GAHNvB,EAAmByG,EAAK3F,KAGMU,IAC1BD,IACCtB,IAAsBD,GACzBuB,EAAKD,IAAmB,GACxBtB,EAAgBsB,IAAoB,GACpCC,EAAKL,GAAOoC,QAAQ,SAAAC,GACfA,EAAQjB,MACXiB,EAAQrC,GAAUqC,EAAQjB,KAE3BiB,EAASa,EAAeb,EAAQjB,SAAcH,CAC/C,KAEAZ,EAAKD,IAAiBgC,QAAQgD,GAC9B/E,EAAKD,IAAiBgC,QAAQiD,GAC9BhF,EAAKD,IAAmB,GACxBvB,EAAe,IAGjBE,EAAoBD,CACrB,EAGAK,EAAQO,OAAS,SAAA6F,GACZ9F,GAAcA,EAAa8F,GAE/B,IAAM5D,EAAI4D,EAAK3F,IACX+B,GAAKA,EAACrB,MACLqB,EAACrB,IAAAF,IAAyBG,SA+ZR,IA/Z2BrB,EAAkBsB,KAAKmB,IA+Z7C3C,IAAYG,EAAQuG,yBAC/C1G,EAAUG,EAAQuG,wBACNC,GAAgBX,IAha5BrD,EAACrB,IAAAN,GAAeoC,QAAQ,SAAAC,GACnBA,EAASa,IACZb,EAAQ/B,IAAS+B,EAASa,GAE3Bb,EAASa,OAAejC,CACzB,IAEDlC,EAAoBD,EAAmB,IACxC,EAIAK,EAAOS,IAAW,SAAC2F,EAAOK,GACzBA,EAAYC,KAAK,SAAAZ,GAChB,IACCA,EAAS7E,IAAkBgC,QAAQgD,GACnCH,EAAS7E,IAAoB6E,EAAS7E,IAAkByB,OAAO,SAAAyC,GAAE,OAChEA,EAAEtE,IAAUqF,EAAaf,EAAU,EAQrC,CANE,MAAOgB,GACRM,EAAYC,KAAK,SAAAlE,GACZA,EAACvB,MAAmBuB,EAACvB,IAAoB,GAC9C,GACAwF,EAAc,GACdzG,EAAOuD,IAAa4C,EAAGL,EAASJ,IACjC,CACD,GAEIlF,GAAWA,EAAU4F,EAAOK,EACjC,EAGAzG,EAAQW,QAAU,SAAAyF,GACb1F,GAAkBA,EAAiB0F,GAEvC,IAEKO,EAFCnE,EAAI4D,EAAK3F,IACX+B,GAAKA,EAACrB,MAETqB,EAACrB,IAAAN,GAAeoC,QAAQ,SAAAV,GACvB,IACC0D,EAAc1D,EAGf,CAFE,MAAO4D,GACRQ,EAAaR,CACd,CACD,GACA3D,EAACrB,SAAWW,EACR6E,GAAY3G,EAAOuD,IAAaoD,EAAYnE,EAACkD,KAEnD,EA2UA,IAAIkB,EAA0C,mBAAzBL,sBAYrB,SAASC,EAAe9C,GACvB,IAOImD,EAPEC,EAAO,WACZC,aAAaC,GACTJ,GAASK,qBAAqBJ,GAClCK,WAAWxD,EACZ,EACMsD,EAAUE,WAAWJ,EAjcR,KAocfF,IACHC,EAAMN,sBAAsBO,GAE9B,CAqBA,SAASb,EAAckB,GAGtB,IAAMC,EAAOzH,EACT0H,EAAUF,EAAI1G,IACI,mBAAX4G,IACVF,EAAI1G,SAAYqB,EAChBuF,KAGD1H,EAAmByH,CACpB,CAOA,SAASlB,EAAaiB,GAGrB,IAAMC,EAAOzH,EACbwH,EAAI1G,IAAY0G,EAAItG,KACpBlB,EAAmByH,CACpB,CAOA,SAAStD,EAAYwD,EAASC,GAC7B,OACED,GACDA,EAAQlG,SAAWmG,EAAQnG,QAC3BmG,EAAQb,KAAK,SAACc,EAAKzG,GAAU,OAAAyG,IAAQF,EAAQvG,EAAM,EAErD,CAQA,SAASU,EAAe+F,EAAKC,GAC5B,MAAmB,mBAALA,EAAkBA,EAAED,GAAOC,CAC1C"}