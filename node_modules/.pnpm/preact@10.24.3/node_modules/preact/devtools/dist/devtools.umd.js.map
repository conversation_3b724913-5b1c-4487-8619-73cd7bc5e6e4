{"version": 3, "file": "devtools.umd.js", "sources": ["../src/devtools.js", "../src/index.js"], "sourcesContent": ["import { Component, Fragment, options } from 'preact';\n\nexport function initDevTools() {\n\tconst globalVar =\n\t\ttypeof globalThis !== 'undefined'\n\t\t\t? globalThis\n\t\t\t: typeof window !== 'undefined'\n\t\t\t\t? window\n\t\t\t\t: undefined;\n\n\tif (\n\t\tglobalVar !== null &&\n\t\tglobalVar !== undefined &&\n\t\tglobalVar.__PREACT_DEVTOOLS__\n\t) {\n\t\tglobalVar.__PREACT_DEVTOOLS__.attachPreact('10.24.3', options, {\n\t\t\tFragment,\n\t\t\tComponent\n\t\t});\n\t}\n}\n", "import { options } from 'preact';\nimport { initDevTools } from './devtools';\n\ninitDevTools();\n\n/**\n * Display a custom label for a custom hook for the devtools panel\n * @type {<T>(value: T, name: string) => T}\n */\nexport function addHookName(value, name) {\n\tif (options._addHookName) {\n\t\toptions._addHookName(name);\n\t}\n\treturn value;\n}\n"], "names": ["globalVar", "globalThis", "window", "undefined", "__PREACT_DEVTOOLS__", "attachPreact", "options", "Fragment", "Component", "value", "name", "__a"], "mappings": "8QAEgB,IACTA,EAQLA,OARKA,EACiB,oBAAfC,WACJA,WACkB,oBAAXC,OACNA,YACAC,IAKJH,EAAUI,qBAEVJ,EAAUI,oBAAoBC,aAAa,UAAWC,EAAOA,QAAE,CAC9DC,SAAAA,EAAAA,SACAC,UAAAA,EAAAA,0BCRa,SAAYC,EAAOC,GAIlC,OAHIJ,EAAAA,QAAOK,KACVL,EAAAA,QAAOK,IAAcD,GAEfD,CACR"}