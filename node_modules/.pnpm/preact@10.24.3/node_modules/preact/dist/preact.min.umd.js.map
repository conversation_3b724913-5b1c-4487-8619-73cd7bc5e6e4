{"version": 3, "file": "preact.min.umd.js", "sources": ["../src/util.js", "../src/options.js", "../src/create-element.js", "../src/component.js", "../src/diff/props.js", "../src/create-context.js", "../src/constants.js", "../src/diff/children.js", "../src/diff/index.js", "../src/render.js", "../src/diff/catch-error.js", "../src/clone-element.js", "../src/cjs.js"], "sourcesContent": ["import { EMPTY_ARR } from './constants';\n\nexport const isArray = Array.isArray;\n\n/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\t// @ts-expect-error We change the type of `obj` to be `O & P`\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Remove a child node from its parent if attached. This is a workaround for\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\n * is smaller than including a dedicated polyfill.\n * @param {preact.ContainerNode} node The node to remove\n */\nexport function removeNode(node) {\n\tif (node && node.parentNode) node.parentNode.removeChild(node);\n}\n\nexport const slice = EMPTY_ARR.slice;\n", "import { _catchError } from './diff/catch-error';\n\n/**\n * The `option` object can potentially contain callback functions\n * that are called during various stages of our renderer. This is the\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\n * for a full list of available option hooks (most editors/IDEs allow you to\n * ctrl+click or cmd+click on mac the type definition below).\n * @type {Options}\n */\nconst options = {\n\t_catchError\n};\n\nexport default options;\n", "import { slice } from './util';\nimport options from './options';\n\nlet vnodeId = 0;\n\n/**\n * Create an virtual node (used for JSX)\n * @param {VNode[\"type\"]} type The node name or Component constructor for this\n * virtual node\n * @param {object | null | undefined} [props] The properties of the virtual node\n * @param {Array<import('.').ComponentChildren>} [children] The children of the\n * virtual node\n * @returns {VNode}\n */\nexport function createElement(type, props, children) {\n\tlet normalizedProps = {},\n\t\tkey,\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\t// If a Component VNode, check for and apply defaultProps\n\t// Note: type may be undefined in development, must never error here.\n\tif (typeof type == 'function' && type.defaultProps != null) {\n\t\tfor (i in type.defaultProps) {\n\t\t\tif (normalizedProps[i] === undefined) {\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn createVNode(type, normalizedProps, key, ref, null);\n}\n\n/**\n * Create a VNode (used internally by Preact)\n * @param {VNode[\"type\"]} type The node name or Component\n * Constructor for this virtual node\n * @param {object | string | number | null} props The properties of this virtual node.\n * If this virtual node represents a text node, this is the text of the node (string or number).\n * @param {string | number | null} key The key for this virtual node, used when\n * diffing it against its children\n * @param {VNode[\"ref\"]} ref The ref property that will\n * receive a reference to its created child\n * @returns {VNode}\n */\nexport function createVNode(type, props, key, ref, original) {\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\n\t// Do not inline into createElement and coerceToVNode!\n\t/** @type {VNode} */\n\tconst vnode = {\n\t\ttype,\n\t\tprops,\n\t\tkey,\n\t\tref,\n\t\t_children: null,\n\t\t_parent: null,\n\t\t_depth: 0,\n\t\t_dom: null,\n\t\t// _nextDom must be initialized to undefined b/c it will eventually\n\t\t// be set to dom.nextSibling which can return `null` and it is important\n\t\t// to be able to distinguish between an uninitialized _nextDom and\n\t\t// a _nextDom that has been set to `null`\n\t\t_nextDom: undefined,\n\t\t_component: null,\n\t\tconstructor: undefined,\n\t\t_original: original == null ? ++vnodeId : original,\n\t\t_index: -1,\n\t\t_flags: 0\n\t};\n\n\t// Only invoke the vnode hook if this was *not* a direct copy:\n\tif (original == null && options.vnode != null) options.vnode(vnode);\n\n\treturn vnode;\n}\n\nexport function createRef() {\n\treturn { current: null };\n}\n\nexport function Fragment(props) {\n\treturn props.children;\n}\n\n/**\n * Check if a the argument is a valid Preact VNode.\n * @param {*} vnode\n * @returns {vnode is VNode}\n */\nexport const isValidElement = vnode =>\n\tvnode != null && vnode.constructor == undefined;\n", "import { assign } from './util';\nimport { diff, commitRoot } from './diff/index';\nimport options from './options';\nimport { Fragment } from './create-element';\nimport { MODE_HYDRATE } from './constants';\n\n/**\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\n * trigger rendering\n * @param {object} props The initial component props\n * @param {object} context The initial context from parent components'\n * getChildContext\n */\nexport function BaseComponent(props, context) {\n\tthis.props = props;\n\tthis.context = context;\n}\n\n/**\n * Update component state and schedule a re-render.\n * @this {Component}\n * @param {object | ((s: object, p: object) => object)} update A hash of state\n * properties to update with new values or a function that given the current\n * state and props returns a new partial state\n * @param {() => void} [callback] A function to be called once component state is\n * updated\n */\nBaseComponent.prototype.setState = function (update, callback) {\n\t// only clone state when copying to nextState the first time.\n\tlet s;\n\tif (this._nextState != null && this._nextState !== this.state) {\n\t\ts = this._nextState;\n\t} else {\n\t\ts = this._nextState = assign({}, this.state);\n\t}\n\n\tif (typeof update == 'function') {\n\t\t// Some libraries like `immer` mark the current state as readonly,\n\t\t// preventing us from mutating it, so we need to clone it. See #2716\n\t\tupdate = update(assign({}, s), this.props);\n\t}\n\n\tif (update) {\n\t\tassign(s, update);\n\t}\n\n\t// Skip update if updater function returned null\n\tif (update == null) return;\n\n\tif (this._vnode) {\n\t\tif (callback) {\n\t\t\tthis._stateCallbacks.push(callback);\n\t\t}\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Immediately perform a synchronous re-render of the component\n * @this {Component}\n * @param {() => void} [callback] A function to be called after component is\n * re-rendered\n */\nBaseComponent.prototype.forceUpdate = function (callback) {\n\tif (this._vnode) {\n\t\t// Set render mode so that we can differentiate where the render request\n\t\t// is coming from. We need this because forceUpdate should never call\n\t\t// shouldComponentUpdate\n\t\tthis._force = true;\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\n * Virtual DOM is generally constructed via [JSX](http://jasonformat.com/wtf-is-jsx).\n * @param {object} props Props (eg: JSX attributes) received from parent\n * element/component\n * @param {object} state The component's current state\n * @param {object} context Context object, as returned by the nearest\n * ancestor's `getChildContext()`\n * @returns {ComponentChildren | void}\n */\nBaseComponent.prototype.render = Fragment;\n\n/**\n * @param {VNode} vnode\n * @param {number | null} [childIndex]\n */\nexport function getDomSibling(vnode, childIndex) {\n\tif (childIndex == null) {\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\n\t\treturn vnode._parent\n\t\t\t? getDomSibling(vnode._parent, vnode._index + 1)\n\t\t\t: null;\n\t}\n\n\tlet sibling;\n\tfor (; childIndex < vnode._children.length; childIndex++) {\n\t\tsibling = vnode._children[childIndex];\n\n\t\tif (sibling != null && sibling._dom != null) {\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\n\t\t\treturn sibling._dom;\n\t\t}\n\t}\n\n\t// If we get here, we have not found a DOM node in this vnode's children.\n\t// We must resume from this vnode's sibling (in it's parent _children array)\n\t// Only climb up and search the parent if we aren't searching through a DOM\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\n\t// the search)\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : null;\n}\n\n/**\n * Trigger in-place re-rendering of a component.\n * @param {Component} component The component to rerender\n */\nfunction renderComponent(component) {\n\tlet oldVNode = component._vnode,\n\t\toldDom = oldVNode._dom,\n\t\tcommitQueue = [],\n\t\trefQueue = [];\n\n\tif (component._parentDom) {\n\t\tconst newVNode = assign({}, oldVNode);\n\t\tnewVNode._original = oldVNode._original + 1;\n\t\tif (options.vnode) options.vnode(newVNode);\n\n\t\tdiff(\n\t\t\tcomponent._parentDom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tcomponent._globalContext,\n\t\t\tcomponent._parentDom.namespaceURI,\n\t\t\toldVNode._flags & MODE_HYDRATE ? [oldDom] : null,\n\t\t\tcommitQueue,\n\t\t\toldDom == null ? getDomSibling(oldVNode) : oldDom,\n\t\t\t!!(oldVNode._flags & MODE_HYDRATE),\n\t\t\trefQueue\n\t\t);\n\n\t\tnewVNode._original = oldVNode._original;\n\t\tnewVNode._parent._children[newVNode._index] = newVNode;\n\t\tcommitRoot(commitQueue, newVNode, refQueue);\n\n\t\tif (newVNode._dom != oldDom) {\n\t\t\tupdateParentDomPointers(newVNode);\n\t\t}\n\t}\n}\n\n/**\n * @param {VNode} vnode\n */\nfunction updateParentDomPointers(vnode) {\n\tif ((vnode = vnode._parent) != null && vnode._component != null) {\n\t\tvnode._dom = vnode._component.base = null;\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\tlet child = vnode._children[i];\n\t\t\tif (child != null && child._dom != null) {\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn updateParentDomPointers(vnode);\n\t}\n}\n\n/**\n * The render queue\n * @type {Array<Component>}\n */\nlet rerenderQueue = [];\n\n/*\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\n * do, and when their effects will be applied. See the links below for some further reading on designing\n * asynchronous APIs.\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\n */\n\nlet prevDebounce;\n\nconst defer =\n\ttypeof Promise == 'function'\n\t\t? Promise.prototype.then.bind(Promise.resolve())\n\t\t: setTimeout;\n\n/**\n * Enqueue a rerender of a component\n * @param {Component} c The component to rerender\n */\nexport function enqueueRender(c) {\n\tif (\n\t\t(!c._dirty &&\n\t\t\t(c._dirty = true) &&\n\t\t\trerenderQueue.push(c) &&\n\t\t\t!process._rerenderCount++) ||\n\t\tprevDebounce !== options.debounceRendering\n\t) {\n\t\tprevDebounce = options.debounceRendering;\n\t\t(prevDebounce || defer)(process);\n\t}\n}\n\n/**\n * @param {Component} a\n * @param {Component} b\n */\nconst depthSort = (a, b) => a._vnode._depth - b._vnode._depth;\n\n/** Flush the render queue by rerendering all queued components */\nfunction process() {\n\tlet c;\n\trerenderQueue.sort(depthSort);\n\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\n\t// process() calls from getting scheduled while `queue` is still being consumed.\n\twhile ((c = rerenderQueue.shift())) {\n\t\tif (c._dirty) {\n\t\t\tlet renderQueueLength = rerenderQueue.length;\n\t\t\trenderComponent(c);\n\t\t\tif (rerenderQueue.length > renderQueueLength) {\n\t\t\t\t// When i.e. rerendering a provider additional new items can be injected, we want to\n\t\t\t\t// keep the order from top to bottom with those new items so we can handle them in a\n\t\t\t\t// single pass\n\t\t\t\trerenderQueue.sort(depthSort);\n\t\t\t}\n\t\t}\n\t}\n\tprocess._rerenderCount = 0;\n}\n\nprocess._rerenderCount = 0;\n", "import { IS_NON_DIMENSIONAL } from '../constants';\nimport options from '../options';\n\nfunction setStyle(style, key, value) {\n\tif (key[0] === '-') {\n\t\tstyle.setProperty(key, value == null ? '' : value);\n\t} else if (value == null) {\n\t\tstyle[key] = '';\n\t} else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\n\t\tstyle[key] = value;\n\t} else {\n\t\tstyle[key] = value + 'px';\n\t}\n}\n\n// A logical clock to solve issues like https://github.com/preactjs/preact/issues/3927.\n// When the DOM performs an event it leaves micro-ticks in between bubbling up which means that\n// an event can trigger on a newly reated DOM-node while the event bubbles up.\n//\n// Originally inspired by Vue\n// (https://github.com/vuejs/core/blob/caeb8a68811a1b0f79/packages/runtime-dom/src/modules/events.ts#L90-L101),\n// but modified to use a logical clock instead of Date.now() in case event handlers get attached\n// and events get dispatched during the same millisecond.\n//\n// The clock is incremented after each new event dispatch. This allows 1 000 000 new events\n// per second for over 280 years before the value reaches Number.MAX_SAFE_INTEGER (2**53 - 1).\nlet eventClock = 0;\n\n/**\n * Set a property value on a DOM node\n * @param {PreactElement} dom The DOM node to modify\n * @param {string} name The name of the property to set\n * @param {*} value The value to set the property to\n * @param {*} oldValue The old value the property had\n * @param {string} namespace Whether or not this DOM node is an SVG node or not\n */\nexport function setProperty(dom, name, value, oldValue, namespace) {\n\tlet useCapture;\n\n\to: if (name === 'style') {\n\t\tif (typeof value == 'string') {\n\t\t\tdom.style.cssText = value;\n\t\t} else {\n\t\t\tif (typeof oldValue == 'string') {\n\t\t\t\tdom.style.cssText = oldValue = '';\n\t\t\t}\n\n\t\t\tif (oldValue) {\n\t\t\t\tfor (name in oldValue) {\n\t\t\t\t\tif (!(value && name in value)) {\n\t\t\t\t\t\tsetStyle(dom.style, name, '');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (value) {\n\t\t\t\tfor (name in value) {\n\t\t\t\t\tif (!oldValue || value[name] !== oldValue[name]) {\n\t\t\t\t\t\tsetStyle(dom.style, name, value[name]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n\telse if (name[0] === 'o' && name[1] === 'n') {\n\t\tuseCapture =\n\t\t\tname !== (name = name.replace(/(PointerCapture)$|Capture$/i, '$1'));\n\n\t\t// Infer correct casing for DOM built-in events:\n\t\tif (\n\t\t\tname.toLowerCase() in dom ||\n\t\t\tname === 'onFocusOut' ||\n\t\t\tname === 'onFocusIn'\n\t\t)\n\t\t\tname = name.toLowerCase().slice(2);\n\t\telse name = name.slice(2);\n\n\t\tif (!dom._listeners) dom._listeners = {};\n\t\tdom._listeners[name + useCapture] = value;\n\n\t\tif (value) {\n\t\t\tif (!oldValue) {\n\t\t\t\tvalue._attached = eventClock;\n\t\t\t\tdom.addEventListener(\n\t\t\t\t\tname,\n\t\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\t\tuseCapture\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tvalue._attached = oldValue._attached;\n\t\t\t}\n\t\t} else {\n\t\t\tdom.removeEventListener(\n\t\t\t\tname,\n\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\tuseCapture\n\t\t\t);\n\t\t}\n\t} else {\n\t\tif (namespace == 'http://www.w3.org/2000/svg') {\n\t\t\t// Normalize incorrect prop usage for SVG:\n\t\t\t// - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\n\t\t\t// - className --> class\n\t\t\tname = name.replace(/xlink(H|:h)/, 'h').replace(/sName$/, 's');\n\t\t} else if (\n\t\t\tname != 'width' &&\n\t\t\tname != 'height' &&\n\t\t\tname != 'href' &&\n\t\t\tname != 'list' &&\n\t\t\tname != 'form' &&\n\t\t\t// Default value in browsers is `-1` and an empty string is\n\t\t\t// cast to `0` instead\n\t\t\tname != 'tabIndex' &&\n\t\t\tname != 'download' &&\n\t\t\tname != 'rowSpan' &&\n\t\t\tname != 'colSpan' &&\n\t\t\tname != 'role' &&\n\t\t\tname != 'popover' &&\n\t\t\tname in dom\n\t\t) {\n\t\t\ttry {\n\t\t\t\tdom[name] = value == null ? '' : value;\n\t\t\t\t// labelled break is 1b smaller here than a return statement (sorry)\n\t\t\t\tbreak o;\n\t\t\t} catch (e) {}\n\t\t}\n\n\t\t// aria- and data- attributes have no boolean representation.\n\t\t// A `false` value is different from the attribute not being\n\t\t// present, so we can't remove it. For non-boolean aria\n\t\t// attributes we could treat false as a removal, but the\n\t\t// amount of exceptions would cost too many bytes. On top of\n\t\t// that other frameworks generally stringify `false`.\n\n\t\tif (typeof value == 'function') {\n\t\t\t// never serialize functions as attribute values\n\t\t} else if (value != null && (value !== false || name[4] === '-')) {\n\t\t\tdom.setAttribute(name, name == 'popover' && value == true ? '' : value);\n\t\t} else {\n\t\t\tdom.removeAttribute(name);\n\t\t}\n\t}\n}\n\n/**\n * Create an event proxy function.\n * @param {boolean} useCapture Is the event handler for the capture phase.\n * @private\n */\nfunction createEventProxy(useCapture) {\n\t/**\n\t * Proxy an event to hooked event handlers\n\t * @param {PreactEvent} e The event object from the browser\n\t * @private\n\t */\n\treturn function (e) {\n\t\tif (this._listeners) {\n\t\t\tconst eventHandler = this._listeners[e.type + useCapture];\n\t\t\tif (e._dispatched == null) {\n\t\t\t\te._dispatched = eventClock++;\n\n\t\t\t\t// When `e._dispatched` is smaller than the time when the targeted event\n\t\t\t\t// handler was attached we know we have bubbled up to an element that was added\n\t\t\t\t// during patching the DOM.\n\t\t\t} else if (e._dispatched < eventHandler._attached) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\treturn eventHandler(options.event ? options.event(e) : e);\n\t\t}\n\t};\n}\n\nconst eventProxy = createEventProxy(false);\nconst eventProxyCapture = createEventProxy(true);\n", "import { enqueueRender } from './component';\n\nexport let i = 0;\n\nexport function createContext(defaultValue, contextId) {\n\tcontextId = '__cC' + i++;\n\n\tconst context = {\n\t\t_id: contextId,\n\t\t_defaultValue: defaultValue,\n\t\t/** @type {FunctionComponent} */\n\t\tConsumer(props, contextValue) {\n\t\t\t// return props.children(\n\t\t\t// \tcontext[contextId] ? context[contextId].props.value : defaultValue\n\t\t\t// );\n\t\t\treturn props.children(contextValue);\n\t\t},\n\t\t/** @type {FunctionComponent} */\n\t\tProvider(props) {\n\t\t\tif (!this.getChildContext) {\n\t\t\t\t/** @type {Set<Component> | null} */\n\t\t\t\tlet subs = new Set();\n\t\t\t\tlet ctx = {};\n\t\t\t\tctx[contextId] = this;\n\n\t\t\t\tthis.getChildContext = () => ctx;\n\n\t\t\t\tthis.componentWillUnmount = () => {\n\t\t\t\t\tsubs = null;\n\t\t\t\t};\n\n\t\t\t\tthis.shouldComponentUpdate = function (_props) {\n\t\t\t\t\tif (this.props.value !== _props.value) {\n\t\t\t\t\t\tsubs.forEach(c => {\n\t\t\t\t\t\t\tc._force = true;\n\t\t\t\t\t\t\tenqueueRender(c);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\tthis.sub = c => {\n\t\t\t\t\tsubs.add(c);\n\t\t\t\t\tlet old = c.componentWillUnmount;\n\t\t\t\t\tc.componentWillUnmount = () => {\n\t\t\t\t\t\tif (subs) {\n\t\t\t\t\t\t\tsubs.delete(c);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (old) old.call(c);\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn props.children;\n\t\t}\n\t};\n\n\t// Devtools needs access to the context object when it\n\t// encounters a Provider. This is necessary to support\n\t// setting `displayName` on the context object instead\n\t// of on the component itself. See:\n\t// https://reactjs.org/docs/context.html#contextdisplayname\n\n\treturn (context.Provider._contextRef = context.Consumer.contextType =\n\t\tcontext);\n}\n", "/** Normal hydration that attaches to a DOM tree but does not diff it. */\nexport const MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nexport const MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nexport const INSERT_VNODE = 1 << 16;\n/** Indicates a VNode has been matched with another VNode in the diff */\nexport const MATCHED = 1 << 17;\n\n/** Reset all mode flags */\nexport const RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\n\nexport const EMPTY_OBJ = /** @type {any} */ ({});\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL =\n\t/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { diff, unmount, applyRef } from './index';\nimport { createVNode, Fragment } from '../create-element';\nimport { EMPTY_OBJ, EMPTY_ARR, INSERT_VNODE, MATCHED } from '../constants';\nimport { isArray } from '../util';\nimport { getDomSibling } from '../component';\n\n/**\n * Diff the children of a virtual node\n * @param {PreactElement} parentDom The DOM element whose children are being\n * diffed\n * @param {ComponentChildren[]} renderResult\n * @param {VNode} newParentVNode The new virtual node whose children should be\n * diff'ed against oldParentVNode\n * @param {VNode} oldParentVNode The old virtual node whose children should be\n * diff'ed against newParentVNode\n * @param {object} globalContext The current context object - modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diffChildren(\n\tparentDom,\n\trenderResult,\n\tnewParentVNode,\n\toldParentVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\tlet i,\n\t\t/** @type {VNode} */\n\t\toldVNode,\n\t\t/** @type {VNode} */\n\t\tchildVNode,\n\t\t/** @type {PreactElement} */\n\t\tnewDom,\n\t\t/** @type {PreactElement} */\n\t\tfirstChildDom;\n\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\n\t// as EMPTY_OBJ._children should be `undefined`.\n\t/** @type {VNode[]} */\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\n\n\tlet newChildrenLength = renderResult.length;\n\n\tnewParentVNode._nextDom = oldDom;\n\tconstructNewChildrenArray(newParentVNode, renderResult, oldChildren);\n\toldDom = newParentVNode._nextDom;\n\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\tchildVNode = newParentVNode._children[i];\n\t\tif (childVNode == null) continue;\n\n\t\t// At this point, constructNewChildrenArray has assigned _index to be the\n\t\t// matchingIndex for this VNode's oldVNode (or -1 if there is no oldVNode).\n\t\tif (childVNode._index === -1) {\n\t\t\toldVNode = EMPTY_OBJ;\n\t\t} else {\n\t\t\toldVNode = oldChildren[childVNode._index] || EMPTY_OBJ;\n\t\t}\n\n\t\t// Update childVNode._index to its final index\n\t\tchildVNode._index = i;\n\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\n\t\tdiff(\n\t\t\tparentDom,\n\t\t\tchildVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\toldDom,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\n\t\t// Adjust DOM nodes\n\t\tnewDom = childVNode._dom;\n\t\tif (childVNode.ref && oldVNode.ref != childVNode.ref) {\n\t\t\tif (oldVNode.ref) {\n\t\t\t\tapplyRef(oldVNode.ref, null, childVNode);\n\t\t\t}\n\t\t\trefQueue.push(\n\t\t\t\tchildVNode.ref,\n\t\t\t\tchildVNode._component || newDom,\n\t\t\t\tchildVNode\n\t\t\t);\n\t\t}\n\n\t\tif (firstChildDom == null && newDom != null) {\n\t\t\tfirstChildDom = newDom;\n\t\t}\n\n\t\tif (\n\t\t\tchildVNode._flags & INSERT_VNODE ||\n\t\t\toldVNode._children === childVNode._children\n\t\t) {\n\t\t\toldDom = insert(childVNode, oldDom, parentDom);\n\t\t} else if (\n\t\t\ttypeof childVNode.type == 'function' &&\n\t\t\tchildVNode._nextDom !== undefined\n\t\t) {\n\t\t\t// Since Fragments or components that return Fragment like VNodes can\n\t\t\t// contain multiple DOM nodes as the same level, continue the diff from\n\t\t\t// the sibling of last DOM child of this child VNode\n\t\t\toldDom = childVNode._nextDom;\n\t\t} else if (newDom) {\n\t\t\toldDom = newDom.nextSibling;\n\t\t}\n\n\t\t// Eagerly cleanup _nextDom. We don't need to persist the value because it\n\t\t// is only used by `diffChildren` to determine where to resume the diff\n\t\t// after diffing Components and Fragments. Once we store it the nextDOM\n\t\t// local var, we can clean up the property. Also prevents us hanging on to\n\t\t// DOM nodes that may have been unmounted.\n\t\tchildVNode._nextDom = undefined;\n\n\t\t// Unset diffing flags\n\t\tchildVNode._flags &= ~(INSERT_VNODE | MATCHED);\n\t}\n\n\t// TODO: With new child diffing algo, consider alt ways to diff Fragments.\n\t// Such as dropping oldDom and moving fragments in place\n\t//\n\t// Because the newParentVNode is Fragment-like, we need to set it's\n\t// _nextDom property to the nextSibling of its last child DOM node.\n\t//\n\t// `oldDom` contains the correct value here because if the last child\n\t// is a Fragment-like, then oldDom has already been set to that child's _nextDom.\n\t// If the last child is a DOM VNode, then oldDom will be set to that DOM\n\t// node's nextSibling.\n\tnewParentVNode._nextDom = oldDom;\n\tnewParentVNode._dom = firstChildDom;\n}\n\n/**\n * @param {VNode} newParentVNode\n * @param {ComponentChildren[]} renderResult\n * @param {VNode[]} oldChildren\n */\nfunction constructNewChildrenArray(newParentVNode, renderResult, oldChildren) {\n\t/** @type {number} */\n\tlet i;\n\t/** @type {VNode} */\n\tlet childVNode;\n\t/** @type {VNode} */\n\tlet oldVNode;\n\n\tconst newChildrenLength = renderResult.length;\n\tlet oldChildrenLength = oldChildren.length,\n\t\tremainingOldChildren = oldChildrenLength;\n\n\tlet skew = 0;\n\n\tnewParentVNode._children = [];\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\t// @ts-expect-error We are reusing the childVNode variable to hold both the\n\t\t// pre and post normalized childVNode\n\t\tchildVNode = renderResult[i];\n\n\t\tif (\n\t\t\tchildVNode == null ||\n\t\t\ttypeof childVNode == 'boolean' ||\n\t\t\ttypeof childVNode == 'function'\n\t\t) {\n\t\t\tchildVNode = newParentVNode._children[i] = null;\n\t\t\tcontinue;\n\t\t}\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\n\t\t// it's own DOM & etc. pointers\n\t\telse if (\n\t\t\ttypeof childVNode == 'string' ||\n\t\t\ttypeof childVNode == 'number' ||\n\t\t\t// eslint-disable-next-line valid-typeof\n\t\t\ttypeof childVNode == 'bigint' ||\n\t\t\tchildVNode.constructor == String\n\t\t) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tnull,\n\t\t\t\tchildVNode,\n\t\t\t\tnull,\n\t\t\t\tnull,\n\t\t\t\tnull\n\t\t\t);\n\t\t} else if (isArray(childVNode)) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tFragment,\n\t\t\t\t{ children: childVNode },\n\t\t\t\tnull,\n\t\t\t\tnull,\n\t\t\t\tnull\n\t\t\t);\n\t\t} else if (childVNode.constructor === undefined && childVNode._depth > 0) {\n\t\t\t// VNode is already in use, clone it. This can happen in the following\n\t\t\t// scenario:\n\t\t\t//   const reuse = <div />\n\t\t\t//   <div>{reuse}<span />{reuse}</div>\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tchildVNode.type,\n\t\t\t\tchildVNode.props,\n\t\t\t\tchildVNode.key,\n\t\t\t\tchildVNode.ref ? childVNode.ref : null,\n\t\t\t\tchildVNode._original\n\t\t\t);\n\t\t} else {\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\n\t\t}\n\n\t\tconst skewedIndex = i + skew;\n\t\tchildVNode._parent = newParentVNode;\n\t\tchildVNode._depth = newParentVNode._depth + 1;\n\n\t\t// Temporarily store the matchingIndex on the _index property so we can pull\n\t\t// out the oldVNode in diffChildren. We'll override this to the VNode's\n\t\t// final index after using this property to get the oldVNode\n\t\tconst matchingIndex = (childVNode._index = findMatchingIndex(\n\t\t\tchildVNode,\n\t\t\toldChildren,\n\t\t\tskewedIndex,\n\t\t\tremainingOldChildren\n\t\t));\n\n\t\toldVNode = null;\n\t\tif (matchingIndex !== -1) {\n\t\t\toldVNode = oldChildren[matchingIndex];\n\t\t\tremainingOldChildren--;\n\t\t\tif (oldVNode) {\n\t\t\t\toldVNode._flags |= MATCHED;\n\t\t\t}\n\t\t}\n\n\t\t// Here, we define isMounting for the purposes of the skew diffing\n\t\t// algorithm. Nodes that are unsuspending are considered mounting and we detect\n\t\t// this by checking if oldVNode._original === null\n\t\tconst isMounting = oldVNode == null || oldVNode._original === null;\n\n\t\tif (isMounting) {\n\t\t\tif (matchingIndex == -1) {\n\t\t\t\tskew--;\n\t\t\t}\n\n\t\t\t// If we are mounting a DOM VNode, mark it for insertion\n\t\t\tif (typeof childVNode.type != 'function') {\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t} else if (matchingIndex !== skewedIndex) {\n\t\t\t// When we move elements around i.e. [0, 1, 2] --> [1, 0, 2]\n\t\t\t// --> we diff 1, we find it at position 1 while our skewed index is 0 and our skew is 0\n\t\t\t//     we set the skew to 1 as we found an offset.\n\t\t\t// --> we diff 0, we find it at position 0 while our skewed index is at 2 and our skew is 1\n\t\t\t//     this makes us increase the skew again.\n\t\t\t// --> we diff 2, we find it at position 2 while our skewed index is at 4 and our skew is 2\n\t\t\t//\n\t\t\t// this becomes an optimization question where currently we see a 1 element offset as an insertion\n\t\t\t// or deletion i.e. we optimize for [0, 1, 2] --> [9, 0, 1, 2]\n\t\t\t// while a more than 1 offset we see as a swap.\n\t\t\t// We could probably build heuristics for having an optimized course of action here as well, but\n\t\t\t// might go at the cost of some bytes.\n\t\t\t//\n\t\t\t// If we wanted to optimize for i.e. only swaps we'd just do the last two code-branches and have\n\t\t\t// only the first item be a re-scouting and all the others fall in their skewed counter-part.\n\t\t\t// We could also further optimize for swaps\n\t\t\tif (matchingIndex == skewedIndex - 1) {\n\t\t\t\tskew--;\n\t\t\t} else if (matchingIndex == skewedIndex + 1) {\n\t\t\t\tskew++;\n\t\t\t} else {\n\t\t\t\tif (matchingIndex > skewedIndex) {\n\t\t\t\t\tskew--;\n\t\t\t\t} else {\n\t\t\t\t\tskew++;\n\t\t\t\t}\n\n\t\t\t\t// Move this VNode's DOM if the original index (matchingIndex) doesn't\n\t\t\t\t// match the new skew index (i + new skew)\n\t\t\t\t// In the former two branches we know that it matches after skewing\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Remove remaining oldChildren if there are any. Loop forwards so that as we\n\t// unmount DOM from the beginning of the oldChildren, we can adjust oldDom to\n\t// point to the next child, which needs to be the first DOM node that won't be\n\t// unmounted.\n\tif (remainingOldChildren) {\n\t\tfor (i = 0; i < oldChildrenLength; i++) {\n\t\t\toldVNode = oldChildren[i];\n\t\t\tif (oldVNode != null && (oldVNode._flags & MATCHED) === 0) {\n\t\t\t\tif (oldVNode._dom == newParentVNode._nextDom) {\n\t\t\t\t\tnewParentVNode._nextDom = getDomSibling(oldVNode);\n\t\t\t\t}\n\n\t\t\t\tunmount(oldVNode, oldVNode);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * @param {VNode} parentVNode\n * @param {PreactElement} oldDom\n * @param {PreactElement} parentDom\n * @returns {PreactElement}\n */\nfunction insert(parentVNode, oldDom, parentDom) {\n\t// Note: VNodes in nested suspended trees may be missing _children.\n\n\tif (typeof parentVNode.type == 'function') {\n\t\tlet children = parentVNode._children;\n\t\tfor (let i = 0; children && i < children.length; i++) {\n\t\t\tif (children[i]) {\n\t\t\t\t// If we enter this code path on sCU bailout, where we copy\n\t\t\t\t// oldVNode._children to newVNode._children, we need to update the old\n\t\t\t\t// children's _parent pointer to point to the newVNode (parentVNode\n\t\t\t\t// here).\n\t\t\t\tchildren[i]._parent = parentVNode;\n\t\t\t\toldDom = insert(children[i], oldDom, parentDom);\n\t\t\t}\n\t\t}\n\n\t\treturn oldDom;\n\t} else if (parentVNode._dom != oldDom) {\n\t\tif (oldDom && parentVNode.type && !parentDom.contains(oldDom)) {\n\t\t\toldDom = getDomSibling(parentVNode);\n\t\t}\n\t\tparentDom.insertBefore(parentVNode._dom, oldDom || null);\n\t\toldDom = parentVNode._dom;\n\t}\n\n\tdo {\n\t\toldDom = oldDom && oldDom.nextSibling;\n\t} while (oldDom != null && oldDom.nodeType === 8);\n\n\treturn oldDom;\n}\n\n/**\n * Flatten and loop through the children of a virtual node\n * @param {ComponentChildren} children The unflattened children of a virtual\n * node\n * @returns {VNode[]}\n */\nexport function toChildArray(children, out) {\n\tout = out || [];\n\tif (children == null || typeof children == 'boolean') {\n\t} else if (isArray(children)) {\n\t\tchildren.some(child => {\n\t\t\ttoChildArray(child, out);\n\t\t});\n\t} else {\n\t\tout.push(children);\n\t}\n\treturn out;\n}\n\n/**\n * @param {VNode} childVNode\n * @param {VNode[]} oldChildren\n * @param {number} skewedIndex\n * @param {number} remainingOldChildren\n * @returns {number}\n */\nfunction findMatchingIndex(\n\tchildVNode,\n\toldChildren,\n\tskewedIndex,\n\tremainingOldChildren\n) {\n\tconst key = childVNode.key;\n\tconst type = childVNode.type;\n\tlet x = skewedIndex - 1;\n\tlet y = skewedIndex + 1;\n\tlet oldVNode = oldChildren[skewedIndex];\n\n\t// We only need to perform a search if there are more children\n\t// (remainingOldChildren) to search. However, if the oldVNode we just looked\n\t// at skewedIndex was not already used in this diff, then there must be at\n\t// least 1 other (so greater than 1) remainingOldChildren to attempt to match\n\t// against. So the following condition checks that ensuring\n\t// remainingOldChildren > 1 if the oldVNode is not already used/matched. Else\n\t// if the oldVNode was null or matched, then there could needs to be at least\n\t// 1 (aka `remainingOldChildren > 0`) children to find and compare against.\n\tlet shouldSearch =\n\t\tremainingOldChildren >\n\t\t(oldVNode != null && (oldVNode._flags & MATCHED) === 0 ? 1 : 0);\n\n\tif (\n\t\toldVNode === null ||\n\t\t(oldVNode &&\n\t\t\tkey == oldVNode.key &&\n\t\t\ttype === oldVNode.type &&\n\t\t\t(oldVNode._flags & MATCHED) === 0)\n\t) {\n\t\treturn skewedIndex;\n\t} else if (shouldSearch) {\n\t\twhile (x >= 0 || y < oldChildren.length) {\n\t\t\tif (x >= 0) {\n\t\t\t\toldVNode = oldChildren[x];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) === 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype === oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn x;\n\t\t\t\t}\n\t\t\t\tx--;\n\t\t\t}\n\n\t\t\tif (y < oldChildren.length) {\n\t\t\t\toldVNode = oldChildren[y];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) === 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype === oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn y;\n\t\t\t\t}\n\t\t\t\ty++;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn -1;\n}\n", "import {\n\tEMPTY_OBJ,\n\tMODE_HYDRATE,\n\tMODE_SUSPENDED,\n\tRESET_MODE\n} from '../constants';\nimport { BaseComponent, getDomSibling } from '../component';\nimport { Fragment } from '../create-element';\nimport { diffChildren } from './children';\nimport { setProperty } from './props';\nimport { assign, isArray, removeNode, slice } from '../util';\nimport options from '../options';\n\n/**\n * Diff two virtual nodes and apply proper changes to the DOM\n * @param {PreactElement} parentDom The parent of the DOM element\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object. Modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diff(\n\tparentDom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\t/** @type {any} */\n\tlet tmp,\n\t\tnewType = newVNode.type;\n\n\t// When passing through createElement it assigns the object\n\t// constructor as undefined. This to prevent JSON-injection.\n\tif (newVNode.constructor !== undefined) return null;\n\n\t// If the previous diff bailed out, resume creating/hydrating.\n\tif (oldVNode._flags & MODE_SUSPENDED) {\n\t\tisHydrating = !!(oldVNode._flags & MODE_HYDRATE);\n\t\toldDom = newVNode._dom = oldVNode._dom;\n\t\texcessDomChildren = [oldDom];\n\t}\n\n\tif ((tmp = options._diff)) tmp(newVNode);\n\n\touter: if (typeof newType == 'function') {\n\t\ttry {\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\n\t\t\tlet newProps = newVNode.props;\n\t\t\tconst isClassComponent =\n\t\t\t\t'prototype' in newType && newType.prototype.render;\n\n\t\t\t// Necessary for createContext api. Setting this property will pass\n\t\t\t// the context value as `this.context` just for this component.\n\t\t\ttmp = newType.contextType;\n\t\t\tlet provider = tmp && globalContext[tmp._id];\n\t\t\tlet componentContext = tmp\n\t\t\t\t? provider\n\t\t\t\t\t? provider.props.value\n\t\t\t\t\t: tmp._defaultValue\n\t\t\t\t: globalContext;\n\n\t\t\t// Get component and set it to `c`\n\t\t\tif (oldVNode._component) {\n\t\t\t\tc = newVNode._component = oldVNode._component;\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\n\t\t\t} else {\n\t\t\t\t// Instantiate the new component\n\t\t\t\tif (isClassComponent) {\n\t\t\t\t\t// @ts-expect-error The check above verifies that newType is suppose to be constructed\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\n\t\t\t\t} else {\n\t\t\t\t\t// @ts-expect-error Trust me, Component implements the interface we want\n\t\t\t\t\tnewVNode._component = c = new BaseComponent(\n\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t);\n\t\t\t\t\tc.constructor = newType;\n\t\t\t\t\tc.render = doRender;\n\t\t\t\t}\n\t\t\t\tif (provider) provider.sub(c);\n\n\t\t\t\tc.props = newProps;\n\t\t\t\tif (!c.state) c.state = {};\n\t\t\t\tc.context = componentContext;\n\t\t\t\tc._globalContext = globalContext;\n\t\t\t\tisNew = c._dirty = true;\n\t\t\t\tc._renderCallbacks = [];\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t}\n\n\t\t\t// Invoke getDerivedStateFromProps\n\t\t\tif (isClassComponent && c._nextState == null) {\n\t\t\t\tc._nextState = c.state;\n\t\t\t}\n\n\t\t\tif (isClassComponent && newType.getDerivedStateFromProps != null) {\n\t\t\t\tif (c._nextState == c.state) {\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\n\t\t\t\t}\n\n\t\t\t\tassign(\n\t\t\t\t\tc._nextState,\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\toldProps = c.props;\n\t\t\toldState = c.state;\n\t\t\tc._vnode = newVNode;\n\n\t\t\t// Invoke pre-render lifecycle methods\n\t\t\tif (isNew) {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tc.componentWillMount != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillMount();\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidMount != null) {\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tnewProps !== oldProps &&\n\t\t\t\t\tc.componentWillReceiveProps != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t!c._force &&\n\t\t\t\t\t((c.shouldComponentUpdate != null &&\n\t\t\t\t\t\tc.shouldComponentUpdate(\n\t\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\t\tc._nextState,\n\t\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t\t) === false) ||\n\t\t\t\t\t\tnewVNode._original === oldVNode._original)\n\t\t\t\t) {\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\n\t\t\t\t\tif (newVNode._original !== oldVNode._original) {\n\t\t\t\t\t\t// When we are dealing with a bail because of sCU we have to update\n\t\t\t\t\t\t// the props, state and dirty-state.\n\t\t\t\t\t\t// when we are dealing with strict-equality we don't as the child could still\n\t\t\t\t\t\t// be dirtied see #3883\n\t\t\t\t\t\tc.props = newProps;\n\t\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t\t\tc._dirty = false;\n\t\t\t\t\t}\n\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t\tnewVNode._children.some(vnode => {\n\t\t\t\t\t\tif (vnode) vnode._parent = newVNode;\n\t\t\t\t\t});\n\n\t\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t\t}\n\t\t\t\t\tc._stateCallbacks = [];\n\n\t\t\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\t\t\tcommitQueue.push(c);\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\n\t\t\t\tif (c.componentWillUpdate != null) {\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidUpdate != null) {\n\t\t\t\t\tc._renderCallbacks.push(() => {\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tc.context = componentContext;\n\t\t\tc.props = newProps;\n\t\t\tc._parentDom = parentDom;\n\t\t\tc._force = false;\n\n\t\t\tlet renderHook = options._render,\n\t\t\t\tcount = 0;\n\t\t\tif (isClassComponent) {\n\t\t\t\tc.state = c._nextState;\n\t\t\t\tc._dirty = false;\n\n\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t}\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t} else {\n\t\t\t\tdo {\n\t\t\t\t\tc._dirty = false;\n\t\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\t\t// Handle setState called in render, see #2553\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t} while (c._dirty && ++count < 25);\n\t\t\t}\n\n\t\t\t// Handle setState called in render, see #2553\n\t\t\tc.state = c._nextState;\n\n\t\t\tif (c.getChildContext != null) {\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\n\t\t\t}\n\n\t\t\tif (isClassComponent && !isNew && c.getSnapshotBeforeUpdate != null) {\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\n\t\t\t}\n\n\t\t\tlet isTopLevelFragment =\n\t\t\t\ttmp != null && tmp.type === Fragment && tmp.key == null;\n\t\t\tlet renderResult = isTopLevelFragment ? tmp.props.children : tmp;\n\n\t\t\tdiffChildren(\n\t\t\t\tparentDom,\n\t\t\t\tisArray(renderResult) ? renderResult : [renderResult],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnamespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\toldDom,\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\tc.base = newVNode._dom;\n\n\t\t\t// We successfully rendered this VNode, unset any stored hydration/bailout state:\n\t\t\tnewVNode._flags &= RESET_MODE;\n\n\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\tcommitQueue.push(c);\n\t\t\t}\n\n\t\t\tif (clearProcessingException) {\n\t\t\t\tc._pendingError = c._processingException = null;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tnewVNode._original = null;\n\t\t\t// if hydrating or creating initial tree, bailout preserves DOM:\n\t\t\tif (isHydrating || excessDomChildren != null) {\n\t\t\t\tnewVNode._flags |= isHydrating\n\t\t\t\t\t? MODE_HYDRATE | MODE_SUSPENDED\n\t\t\t\t\t: MODE_SUSPENDED;\n\n\t\t\t\twhile (oldDom && oldDom.nodeType === 8 && oldDom.nextSibling) {\n\t\t\t\t\toldDom = oldDom.nextSibling;\n\t\t\t\t}\n\t\t\t\texcessDomChildren[excessDomChildren.indexOf(oldDom)] = null;\n\t\t\t\tnewVNode._dom = oldDom;\n\t\t\t} else {\n\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t}\n\t\t\toptions._catchError(e, newVNode, oldVNode);\n\t\t}\n\t} else if (\n\t\texcessDomChildren == null &&\n\t\tnewVNode._original === oldVNode._original\n\t) {\n\t\tnewVNode._children = oldVNode._children;\n\t\tnewVNode._dom = oldVNode._dom;\n\t} else {\n\t\tnewVNode._dom = diffElementNodes(\n\t\t\toldVNode._dom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\t}\n\n\tif ((tmp = options.diffed)) tmp(newVNode);\n}\n\n/**\n * @param {Array<Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {VNode} root\n */\nexport function commitRoot(commitQueue, root, refQueue) {\n\troot._nextDom = undefined;\n\n\tfor (let i = 0; i < refQueue.length; i++) {\n\t\tapplyRef(refQueue[i], refQueue[++i], refQueue[++i]);\n\t}\n\n\tif (options._commit) options._commit(root, commitQueue);\n\n\tcommitQueue.some(c => {\n\t\ttry {\n\t\t\t// @ts-expect-error Reuse the commitQueue variable here so the type changes\n\t\t\tcommitQueue = c._renderCallbacks;\n\t\t\tc._renderCallbacks = [];\n\t\t\tcommitQueue.some(cb => {\n\t\t\t\t// @ts-expect-error See above comment on commitQueue\n\t\t\t\tcb.call(c);\n\t\t\t});\n\t\t} catch (e) {\n\t\t\toptions._catchError(e, c._vnode);\n\t\t}\n\t});\n}\n\n/**\n * Diff two virtual nodes representing DOM element\n * @param {PreactElement} dom The DOM element representing the virtual nodes\n * being diffed\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n * @returns {PreactElement}\n */\nfunction diffElementNodes(\n\tdom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\tisHydrating,\n\trefQueue\n) {\n\tlet oldProps = oldVNode.props;\n\tlet newProps = newVNode.props;\n\tlet nodeType = /** @type {string} */ (newVNode.type);\n\t/** @type {any} */\n\tlet i;\n\t/** @type {{ __html?: string }} */\n\tlet newHtml;\n\t/** @type {{ __html?: string }} */\n\tlet oldHtml;\n\t/** @type {ComponentChildren} */\n\tlet newChildren;\n\tlet value;\n\tlet inputValue;\n\tlet checked;\n\n\t// Tracks entering and exiting namespaces when descending through the tree.\n\tif (nodeType === 'svg') namespace = 'http://www.w3.org/2000/svg';\n\telse if (nodeType === 'math')\n\t\tnamespace = 'http://www.w3.org/1998/Math/MathML';\n\telse if (!namespace) namespace = 'http://www.w3.org/1999/xhtml';\n\n\tif (excessDomChildren != null) {\n\t\tfor (i = 0; i < excessDomChildren.length; i++) {\n\t\t\tvalue = excessDomChildren[i];\n\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\n\t\t\t// argument matches an element in excessDomChildren, remove it from\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\n\t\t\tif (\n\t\t\t\tvalue &&\n\t\t\t\t'setAttribute' in value === !!nodeType &&\n\t\t\t\t(nodeType ? value.localName === nodeType : value.nodeType === 3)\n\t\t\t) {\n\t\t\t\tdom = value;\n\t\t\t\texcessDomChildren[i] = null;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (dom == null) {\n\t\tif (nodeType === null) {\n\t\t\treturn document.createTextNode(newProps);\n\t\t}\n\n\t\tdom = document.createElementNS(\n\t\t\tnamespace,\n\t\t\tnodeType,\n\t\t\tnewProps.is && newProps\n\t\t);\n\n\t\t// we are creating a new node, so we can assume this is a new subtree (in\n\t\t// case we are hydrating), this deopts the hydrate\n\t\tif (isHydrating) {\n\t\t\tif (options._hydrationMismatch)\n\t\t\t\toptions._hydrationMismatch(newVNode, excessDomChildren);\n\t\t\tisHydrating = false;\n\t\t}\n\t\t// we created a new parent, so none of the previously attached children can be reused:\n\t\texcessDomChildren = null;\n\t}\n\n\tif (nodeType === null) {\n\t\t// During hydration, we still have to split merged text from SSR'd HTML.\n\t\tif (oldProps !== newProps && (!isHydrating || dom.data !== newProps)) {\n\t\t\tdom.data = newProps;\n\t\t}\n\t} else {\n\t\t// If excessDomChildren was not null, repopulate it with the current element's children:\n\t\texcessDomChildren = excessDomChildren && slice.call(dom.childNodes);\n\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\n\n\t\t// If we are in a situation where we are not hydrating but are using\n\t\t// existing DOM (e.g. replaceNode) we should read the existing DOM\n\t\t// attributes to diff them\n\t\tif (!isHydrating && excessDomChildren != null) {\n\t\t\toldProps = {};\n\t\t\tfor (i = 0; i < dom.attributes.length; i++) {\n\t\t\t\tvalue = dom.attributes[i];\n\t\t\t\toldProps[value.name] = value.value;\n\t\t\t}\n\t\t}\n\n\t\tfor (i in oldProps) {\n\t\t\tvalue = oldProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\toldHtml = value;\n\t\t\t} else if (!(i in newProps)) {\n\t\t\t\tif (\n\t\t\t\t\t(i == 'value' && 'defaultValue' in newProps) ||\n\t\t\t\t\t(i == 'checked' && 'defaultChecked' in newProps)\n\t\t\t\t) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tsetProperty(dom, i, null, value, namespace);\n\t\t\t}\n\t\t}\n\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\n\t\t// @TODO we should warn in debug mode when props don't match here.\n\t\tfor (i in newProps) {\n\t\t\tvalue = newProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t\tnewChildren = value;\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\tnewHtml = value;\n\t\t\t} else if (i == 'value') {\n\t\t\t\tinputValue = value;\n\t\t\t} else if (i == 'checked') {\n\t\t\t\tchecked = value;\n\t\t\t} else if (\n\t\t\t\t(!isHydrating || typeof value == 'function') &&\n\t\t\t\toldProps[i] !== value\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, value, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\n\t\tif (newHtml) {\n\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\n\t\t\tif (\n\t\t\t\t!isHydrating &&\n\t\t\t\t(!oldHtml ||\n\t\t\t\t\t(newHtml.__html !== oldHtml.__html &&\n\t\t\t\t\t\tnewHtml.__html !== dom.innerHTML))\n\t\t\t) {\n\t\t\t\tdom.innerHTML = newHtml.__html;\n\t\t\t}\n\n\t\t\tnewVNode._children = [];\n\t\t} else {\n\t\t\tif (oldHtml) dom.innerHTML = '';\n\n\t\t\tdiffChildren(\n\t\t\t\tdom,\n\t\t\t\tisArray(newChildren) ? newChildren : [newChildren],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnodeType === 'foreignObject'\n\t\t\t\t\t? 'http://www.w3.org/1999/xhtml'\n\t\t\t\t\t: namespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\texcessDomChildren\n\t\t\t\t\t? excessDomChildren[0]\n\t\t\t\t\t: oldVNode._children && getDomSibling(oldVNode, 0),\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\t// Remove children that are not part of any vnode.\n\t\t\tif (excessDomChildren != null) {\n\t\t\t\tfor (i = excessDomChildren.length; i--; ) {\n\t\t\t\t\tremoveNode(excessDomChildren[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// As above, don't diff props during hydration\n\t\tif (!isHydrating) {\n\t\t\ti = 'value';\n\t\t\tif (nodeType === 'progress' && inputValue == null) {\n\t\t\t\tdom.removeAttribute('value');\n\t\t\t} else if (\n\t\t\t\tinputValue !== undefined &&\n\t\t\t\t// #2756 For the <progress>-element the initial value is 0,\n\t\t\t\t// despite the attribute not being present. When the attribute\n\t\t\t\t// is missing the progress bar is treated as indeterminate.\n\t\t\t\t// To fix that we'll always update it when it is 0 for progress elements\n\t\t\t\t(inputValue !== dom[i] ||\n\t\t\t\t\t(nodeType === 'progress' && !inputValue) ||\n\t\t\t\t\t// This is only for IE 11 to fix <select> value not being updated.\n\t\t\t\t\t// To avoid a stale select value we need to set the option.value\n\t\t\t\t\t// again, which triggers IE11 to re-evaluate the select value\n\t\t\t\t\t(nodeType === 'option' && inputValue !== oldProps[i]))\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, inputValue, oldProps[i], namespace);\n\t\t\t}\n\n\t\t\ti = 'checked';\n\t\t\tif (checked !== undefined && checked !== dom[i]) {\n\t\t\t\tsetProperty(dom, i, checked, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn dom;\n}\n\n/**\n * Invoke or update a ref, depending on whether it is a function or object ref.\n * @param {Ref<any> & { _unmount?: unknown }} ref\n * @param {any} value\n * @param {VNode} vnode\n */\nexport function applyRef(ref, value, vnode) {\n\ttry {\n\t\tif (typeof ref == 'function') {\n\t\t\tlet hasRefUnmount = typeof ref._unmount == 'function';\n\t\t\tif (hasRefUnmount) {\n\t\t\t\t// @ts-ignore TS doesn't like moving narrowing checks into variables\n\t\t\t\tref._unmount();\n\t\t\t}\n\n\t\t\tif (!hasRefUnmount || value != null) {\n\t\t\t\t// Store the cleanup function on the function\n\t\t\t\t// instance object itself to avoid shape\n\t\t\t\t// transitioning vnode\n\t\t\t\tref._unmount = ref(value);\n\t\t\t}\n\t\t} else ref.current = value;\n\t} catch (e) {\n\t\toptions._catchError(e, vnode);\n\t}\n}\n\n/**\n * Unmount a virtual node from the tree and apply DOM changes\n * @param {VNode} vnode The virtual node to unmount\n * @param {VNode} parentVNode The parent of the VNode that initiated the unmount\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\n * current element is already detached from the DOM.\n */\nexport function unmount(vnode, parentVNode, skipRemove) {\n\tlet r;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif ((r = vnode.ref)) {\n\t\tif (!r.current || r.current === vnode._dom) {\n\t\t\tapplyRef(r, null, parentVNode);\n\t\t}\n\t}\n\n\tif ((r = vnode._component) != null) {\n\t\tif (r.componentWillUnmount) {\n\t\t\ttry {\n\t\t\t\tr.componentWillUnmount();\n\t\t\t} catch (e) {\n\t\t\t\toptions._catchError(e, parentVNode);\n\t\t\t}\n\t\t}\n\n\t\tr.base = r._parentDom = null;\n\t}\n\n\tif ((r = vnode._children)) {\n\t\tfor (let i = 0; i < r.length; i++) {\n\t\t\tif (r[i]) {\n\t\t\t\tunmount(\n\t\t\t\t\tr[i],\n\t\t\t\t\tparentVNode,\n\t\t\t\t\tskipRemove || typeof vnode.type != 'function'\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (!skipRemove) {\n\t\tremoveNode(vnode._dom);\n\t}\n\n\t// Must be set to `undefined` to properly clean up `_nextDom`\n\t// for which `null` is a valid value. See comment in `create-element.js`\n\tvnode._component = vnode._parent = vnode._dom = vnode._nextDom = undefined;\n}\n\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n\treturn this.constructor(props, context);\n}\n", "import { EMPTY_OBJ } from './constants';\nimport { commitRoot, diff } from './diff/index';\nimport { createElement, Fragment } from './create-element';\nimport options from './options';\nimport { slice } from './util';\n\n/**\n * Render a Preact virtual node into a DOM element\n * @param {ComponentChild} vnode The virtual node to render\n * @param {PreactElement} parentDom The DOM element to render into\n * @param {PreactElement | object} [replaceNode] Optional: Attempt to re-use an\n * existing DOM tree rooted at `replaceNode`\n */\nexport function render(vnode, parentDom, replaceNode) {\n\tif (options._root) options._root(vnode, parentDom);\n\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\n\t// hydration mode or not by passing the `hydrate` function instead of a DOM\n\t// element..\n\tlet isHydrating = typeof replaceNode == 'function';\n\n\t// To be able to support calling `render()` multiple times on the same\n\t// DOM node, we need to obtain a reference to the previous tree. We do\n\t// this by assigning a new `_children` property to DOM nodes which points\n\t// to the last rendered tree. By default this property is not present, which\n\t// means that we are mounting a new tree for the first time.\n\tlet oldVNode = isHydrating\n\t\t? null\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\n\n\tvnode = ((!isHydrating && replaceNode) || parentDom)._children =\n\t\tcreateElement(Fragment, null, [vnode]);\n\n\t// List of effects that need to be called after diffing.\n\tlet commitQueue = [],\n\t\trefQueue = [];\n\tdiff(\n\t\tparentDom,\n\t\t// Determine the new vnode tree and store it on the DOM element on\n\t\t// our custom `_children` property.\n\t\tvnode,\n\t\toldVNode || EMPTY_OBJ,\n\t\tEMPTY_OBJ,\n\t\tparentDom.namespaceURI,\n\t\t!isHydrating && replaceNode\n\t\t\t? [replaceNode]\n\t\t\t: oldVNode\n\t\t\t\t? null\n\t\t\t\t: parentDom.firstChild\n\t\t\t\t\t? slice.call(parentDom.childNodes)\n\t\t\t\t\t: null,\n\t\tcommitQueue,\n\t\t!isHydrating && replaceNode\n\t\t\t? replaceNode\n\t\t\t: oldVNode\n\t\t\t\t? oldVNode._dom\n\t\t\t\t: parentDom.firstChild,\n\t\tisHydrating,\n\t\trefQueue\n\t);\n\n\t// Flush all queued effects\n\tcommitRoot(commitQueue, vnode, refQueue);\n}\n\n/**\n * Update an existing DOM element with data from a Preact virtual node\n * @param {ComponentChild} vnode The virtual node to render\n * @param {PreactElement} parentDom The DOM element to update\n */\nexport function hydrate(vnode, parentDom) {\n\trender(vnode, parentDom, hydrate);\n}\n", "/**\n * Find the closest error boundary to a thrown error and call it\n * @param {object} error The thrown value\n * @param {VNode} vnode The vnode that threw the error that was caught (except\n * for unmounting when this parameter is the highest parent that was being\n * unmounted)\n * @param {VNode} [oldVNode]\n * @param {ErrorInfo} [errorInfo]\n */\nexport function _catchError(error, vnode, oldVNode, errorInfo) {\n\t/** @type {Component} */\n\tlet component,\n\t\t/** @type {ComponentType} */\n\t\tctor,\n\t\t/** @type {boolean} */\n\t\thandled;\n\n\tfor (; (vnode = vnode._parent); ) {\n\t\tif ((component = vnode._component) && !component._processingException) {\n\t\t\ttry {\n\t\t\t\tctor = component.constructor;\n\n\t\t\t\tif (ctor && ctor.getDerivedStateFromError != null) {\n\t\t\t\t\tcomponent.setState(ctor.getDerivedStateFromError(error));\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\tif (component.componentDidCatch != null) {\n\t\t\t\t\tcomponent.componentDidCatch(error, errorInfo || {});\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\t// This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\n\t\t\t\tif (handled) {\n\t\t\t\t\treturn (component._pendingError = component);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\terror = e;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow error;\n}\n", "import { assign, slice } from './util';\nimport { createVNode } from './create-element';\n\n/**\n * Clones the given VNode, optionally adding attributes/props and replacing its\n * children.\n * @param {VNode} vnode The virtual DOM element to clone\n * @param {object} props Attributes/props to add when cloning\n * @param {Array<ComponentChildren>} rest Any additional arguments will be used\n * as replacement children.\n * @returns {VNode}\n */\nexport function cloneElement(vnode, props, children) {\n\tlet normalizedProps = assign({}, vnode.props),\n\t\tkey,\n\t\tref,\n\t\ti;\n\n\tlet defaultProps;\n\n\tif (vnode.type && vnode.type.defaultProps) {\n\t\tdefaultProps = vnode.type.defaultProps;\n\t}\n\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse if (props[i] === undefined && defaultProps !== undefined) {\n\t\t\tnormalizedProps[i] = defaultProps[i];\n\t\t} else {\n\t\t\tnormalizedProps[i] = props[i];\n\t\t}\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\treturn createVNode(\n\t\tvnode.type,\n\t\tnormalizedProps,\n\t\tkey || vnode.key,\n\t\tref || vnode.ref,\n\t\tnull\n\t);\n}\n", "import * as preact from './index.js';\nif (typeof module < 'u') module.exports = preact;\nelse self.preact = preact;\n"], "names": ["slice", "options", "vnodeId", "isValidElement", "rerenderQueue", "prevDebounce", "defer", "depthSort", "eventClock", "eventProxy", "eventProxyCapture", "i", "INSERT_VNODE", "MATCHED", "EMPTY_OBJ", "EMPTY_ARR", "IS_NON_DIMENSIONAL", "isArray", "Array", "assign", "obj", "props", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "type", "children", "key", "ref", "normalizedProps", "arguments", "length", "call", "defaultProps", "undefined", "createVNode", "original", "vnode", "__k", "__", "__b", "__e", "__d", "__c", "constructor", "__v", "__i", "__u", "Fragment", "BaseComponent", "context", "this", "getDomSibling", "childIndex", "sibling", "updateParentDomPointers", "child", "base", "enqueueRender", "c", "push", "process", "__r", "debounceRendering", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "component", "newVNode", "oldVNode", "oldDom", "commitQueue", "refQueue", "sort", "shift", "__P", "diff", "__n", "namespaceURI", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "parentDom", "renderResult", "newParentVNode", "oldParentVNode", "globalContext", "namespace", "excessDomChildren", "isHydrating", "childVNode", "newDom", "firstChildDom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructNewChildrenArray", "applyRef", "insert", "nextS<PERSON>ling", "skewedIndex", "matchingIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remainingOldChildren", "skew", "String", "findMatchingIndex", "unmount", "parentVNode", "contains", "insertBefore", "nodeType", "x", "y", "setStyle", "style", "value", "setProperty", "test", "dom", "name", "oldValue", "useCapture", "o", "cssText", "replace", "toLowerCase", "l", "_attached", "addEventListener", "removeEventListener", "e", "removeAttribute", "setAttribute", "createEventProxy", "<PERSON><PERSON><PERSON><PERSON>", "_dispatched", "event", "tmp", "isNew", "oldProps", "oldState", "snapshot", "clearProcessingException", "newProps", "isClassComponent", "provider", "componentContext", "renderHook", "count", "newType", "outer", "prototype", "render", "contextType", "__E", "doR<PERSON>", "sub", "state", "__h", "_sb", "__s", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "some", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "MODE_HYDRATE", "indexOf", "diffElementNodes", "diffed", "root", "cb", "newHtml", "oldHtml", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputValue", "checked", "localName", "document", "createTextNode", "createElementNS", "is", "__m", "data", "childNodes", "attributes", "__html", "innerHTML", "hasRefUnmount", "current", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "replaceNode", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "ctor", "handled", "getDerivedStateFromError", "setState", "componentDidCatch", "update", "callback", "s", "forceUpdate", "Promise", "then", "bind", "resolve", "setTimeout", "a", "b", "hydrate", "defaultValue", "contextId", "Consumer", "contextValue", "Provider", "subs", "ctx", "Set", "_props", "for<PERSON>ach", "add", "old", "delete", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "out", "module", "exports", "preact", "self"], "mappings": "iFA2BaA,EChBPC,ECRFC,EAgGSC,EC+ETC,EAWAC,EAEEC,EA0BAC,EC/LFC,EAmJEC,EACAC,EC5KKC,ICGEC,EAAe,MAEfC,EAAU,GAAK,GAKfC,EAAgC,CAAA,EAChCC,EAAY,GACZC,EACZ,oENbYC,EAAUC,MAAMD,QAStB,SAASE,EAAOC,EAAKC,GAE3B,IAAK,IAAIV,KAAKU,EAAOD,EAAIT,GAAKU,EAAMV,GACpC,OAA6BS,CAC9B,CAQgB,SAAAE,EAAWC,GACtBA,GAAQA,EAAKC,YAAYD,EAAKC,WAAWC,YAAYF,EAC1D,CEXO,SAASG,EAAcC,EAAMN,EAAOO,GAC1C,IACCC,EACAC,EACAnB,EAHGoB,EAAkB,CAAA,EAItB,IAAKpB,KAAKU,EACA,OAALV,EAAYkB,EAAMR,EAAMV,GACd,OAALA,EAAYmB,EAAMT,EAAMV,GAC5BoB,EAAgBpB,GAAKU,EAAMV,GAUjC,GAPIqB,UAAUC,OAAS,IACtBF,EAAgBH,SACfI,UAAUC,OAAS,EAAIjC,EAAMkC,KAAKF,UAAW,GAAKJ,GAKjC,mBAARD,GAA2C,MAArBA,EAAKQ,aACrC,IAAKxB,KAAKgB,EAAKQ,kBACaC,IAAvBL,EAAgBpB,KACnBoB,EAAgBpB,GAAKgB,EAAKQ,aAAaxB,IAK1C,OAAO0B,EAAYV,EAAMI,EAAiBF,EAAKC,EAAK,KACrD,CAcO,SAASO,EAAYV,EAAMN,EAAOQ,EAAKC,EAAKQ,GAIlD,IAAMC,EAAQ,CACbZ,KAAAA,EACAN,MAAAA,EACAQ,IAAAA,EACAC,IAAAA,EACAU,IAAW,KACXC,GAAS,KACTC,IAAQ,EACRC,IAAM,KAKNC,SAAUR,EACVS,IAAY,KACZC,iBAAaV,EACbW,IAAuB,MAAZT,IAAqBpC,EAAUoC,EAC1CU,KAAS,EACTC,IAAQ,GAMT,OAFgB,MAAZX,GAAqC,MAAjBrC,EAAQsC,OAAetC,EAAQsC,MAAMA,GAEtDA,CACR,UAMgBW,EAAS7B,GACxB,OAAOA,EAAMO,QACd,UC/EgBuB,EAAc9B,EAAO+B,GACpCC,KAAKhC,MAAQA,EACbgC,KAAKD,QAAUA,CAChB,CA0EgB,SAAAE,EAAcf,EAAOgB,GACpC,GAAkB,MAAdA,EAEH,OAAOhB,EAAKE,GACTa,EAAcf,EAAKE,GAAUF,EAAKS,IAAU,GAC5C,KAIJ,IADA,IAAIQ,EACGD,EAAahB,EAAKC,IAAWP,OAAQsB,IAG3C,GAAe,OAFfC,EAAUjB,EAAKC,IAAWe,KAEa,MAAhBC,EAAOb,IAI7B,OAAOa,EAAOb,IAShB,MAA4B,mBAAdJ,EAAMZ,KAAqB2B,EAAcf,GAAS,IACjE,CA2CA,SAASkB,EAAwBlB,GAAjC,IAGW5B,EACJ+C,EAHN,GAA+B,OAA1BnB,EAAQA,EAAKE,KAAyC,MAApBF,EAAKM,IAAqB,CAEhE,IADAN,EAAKI,IAAQJ,EAAKM,IAAYc,KAAO,KAC5BhD,EAAI,EAAGA,EAAI4B,EAAKC,IAAWP,OAAQtB,IAE3C,GAAa,OADT+C,EAAQnB,EAAKC,IAAW7B,KACO,MAAd+C,EAAKf,IAAe,CACxCJ,EAAKI,IAAQJ,EAAKM,IAAYc,KAAOD,EAAKf,IAC1C,KACD,CAGD,OAAOc,EAAwBlB,EAChC,CACD,CA4BgB,SAAAqB,EAAcC,KAE1BA,EAACjB,MACDiB,EAACjB,KAAU,IACZxC,EAAc0D,KAAKD,KAClBE,EAAOC,OACT3D,IAAiBJ,EAAQgE,sBAEzB5D,EAAeJ,EAAQgE,oBACN3D,GAAOyD,EAE1B,CASA,SAASA,IAAT,IACKF,EAMEK,EAzGkBC,EAOjBC,EANHC,EACHC,EACAC,EACAC,EAmGD,IAHApE,EAAcqE,KAAKlE,GAGXsD,EAAIzD,EAAcsE,SACrBb,EAACjB,MACAsB,EAAoB9D,EAAc6B,OAlGjCmC,SALNE,GADGD,GADoBF,EA0GNN,GAzGMd,KACNJ,IACjB4B,EAAc,GACdC,EAAW,GAERL,EAASQ,OACNP,EAAWjD,EAAO,GAAIkD,IACpBtB,IAAasB,EAAQtB,IAAa,EACtC9C,EAAQsC,OAAOtC,EAAQsC,MAAM6B,GAEjCQ,EACCT,EAASQ,IACTP,EACAC,EACAF,EAASU,IACTV,EAASQ,IAAYG,aGzII,GH0IzBT,EAAQpB,IAAyB,CAACqB,GAAU,KAC5CC,EACU,MAAVD,EAAiBhB,EAAce,GAAYC,KG5IlB,GH6ItBD,EAAQpB,KACXuB,GAGDJ,EAAQrB,IAAasB,EAAQtB,IAC7BqB,EAAQ3B,GAAAD,IAAmB4B,EAAQpB,KAAWoB,EAC9CW,EAAWR,EAAaH,EAAUI,GAE9BJ,EAAQzB,KAAS2B,GACpBb,EAAwBW,IA8EpBhE,EAAc6B,OAASiC,GAI1B9D,EAAcqE,KAAKlE,IAItBwD,EAAOC,IAAkB,CAC1B,CIlNO,SAASgB,EACfC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAhB,EACAD,EACAkB,EACAhB,GAXM,IAaF7D,EAEH0D,EAEAoB,EAEAC,EAEAC,EAKGC,EAAeR,GAAkBA,EAAc5C,KAAezB,EAE9D8E,EAAoBX,EAAajD,OAMrC,IAJAkD,EAAcvC,IAAY0B,EAC1BwB,EAA0BX,EAAgBD,EAAcU,GACxDtB,EAASa,EAAcvC,IAElBjC,EAAI,EAAGA,EAAIkF,EAAmBlF,IAEhB,OADlB8E,EAAaN,EAAc3C,IAAW7B,MAMrC0D,GAD0B,IAAvBoB,EAAUzC,IACFlC,EAEA8E,EAAYH,EAAUzC,MAAYlC,EAI9C2E,EAAUzC,IAAUrC,EAGpBiE,EACCK,EACAQ,EACApB,EACAgB,EACAC,EACAC,EACAhB,EACAD,EACAkB,EACAhB,GAIDkB,EAASD,EAAU9C,IACf8C,EAAW3D,KAAOuC,EAASvC,KAAO2D,EAAW3D,MAC5CuC,EAASvC,KACZiE,EAAS1B,EAASvC,IAAK,KAAM2D,GAE9BjB,EAASV,KACR2B,EAAW3D,IACX2D,EAAU5C,KAAe6C,EACzBD,IAImB,MAAjBE,GAAmC,MAAVD,IAC5BC,EAAgBD,GAIhBD,EAAUxC,IAAUrC,GACpByD,EAAQ7B,MAAeiD,EAAUjD,IAEjC8B,EAAS0B,EAAOP,EAAYnB,EAAQW,GAEV,mBAAnBQ,EAAW9D,WACMS,IAAxBqD,EAAU7C,IAKV0B,EAASmB,EAAU7C,IACT8C,IACVpB,EAASoB,EAAOO,aAQjBR,EAAU7C,SAAYR,EAGtBqD,EAAUxC,MAAW,QAatBkC,EAAcvC,IAAY0B,EAC1Ba,EAAcxC,IAAQgD,CACvB,CAOA,SAASG,EAA0BX,EAAgBD,EAAcU,GAAjE,IAEKjF,EAEA8E,EAEApB,EA+DG6B,EAOAC,EApEDN,EAAoBX,EAAajD,OACnCmE,EAAoBR,EAAY3D,OACnCoE,EAAuBD,EAEpBE,EAAO,EAGX,IADAnB,EAAc3C,IAAa,GACtB7B,EAAI,EAAGA,EAAIkF,EAAmBlF,IAMnB,OAHf8E,EAAaP,EAAavE,KAIJ,kBAAd8E,GACc,mBAAdA,GA8CFS,EAAcvF,EAAI2F,GA/BvBb,EAAaN,EAAc3C,IAAW7B,GANjB,iBAAd8E,GACc,iBAAdA,GAEc,iBAAdA,GACPA,EAAW3C,aAAeyD,OAEiBlE,EAC1C,KACAoD,EACA,KACA,KACA,MAESxE,EAAQwE,GACyBpD,EAC1Ca,EACA,CAAEtB,SAAU6D,GACZ,KACA,KACA,WAEoCrD,IAA3BqD,EAAW3C,aAA6B2C,EAAU/C,IAAU,EAK3BL,EAC1CoD,EAAW9D,KACX8D,EAAWpE,MACXoE,EAAW5D,IACX4D,EAAW3D,IAAM2D,EAAW3D,IAAM,KAClC2D,EAAU1C,KAGgC0C,GAIlChD,GAAW0C,EACrBM,EAAU/C,IAAUyC,EAAczC,IAAU,EAY5C2B,EAAW,MACY,KARjB8B,EAAiBV,EAAUzC,IAAUwD,EAC1Cf,EACAG,EACAM,EACAG,MAMAA,KADAhC,EAAWuB,EAAYO,MAGtB9B,EAAQpB,KAAWpC,IAOU,MAAZwD,GAA2C,OAAvBA,EAAQtB,MAGxB,GAAlBoD,GACHG,IAI6B,mBAAnBb,EAAW9D,OACrB8D,EAAUxC,KAAWrC,IAEZuF,IAAkBD,IAiBxBC,GAAiBD,EAAc,EAClCI,IACUH,GAAiBD,EAAc,EACzCI,KAEIH,EAAgBD,EACnBI,IAEAA,IAMDb,EAAUxC,KAAWrC,KAhHtB6E,EAAaN,EAAc3C,IAAW7B,GAAK,KAyH7C,GAAI0F,EACH,IAAK1F,EAAI,EAAGA,EAAIyF,EAAmBzF,IAElB,OADhB0D,EAAWuB,EAAYjF,KACiC,IAA/B0D,EAAQpB,IAAUpC,KACtCwD,EAAQ1B,KAASwC,EAAcvC,MAClCuC,EAAcvC,IAAYU,EAAce,IAGzCoC,EAAQpC,EAAUA,GAItB,CAQA,SAAS2B,EAAOU,EAAapC,EAAQW,GAArC,IAIMrD,EACKjB,EAFV,GAA+B,mBAApB+F,EAAY/E,KAAoB,CAE1C,IADIC,EAAW8E,EAAWlE,IACjB7B,EAAI,EAAGiB,GAAYjB,EAAIiB,EAASK,OAAQtB,IAC5CiB,EAASjB,KAKZiB,EAASjB,GAAE8B,GAAWiE,EACtBpC,EAAS0B,EAAOpE,EAASjB,GAAI2D,EAAQW,IAIvC,OAAOX,CACR,CAAWoC,EAAW/D,KAAS2B,IAC1BA,GAAUoC,EAAY/E,OAASsD,EAAU0B,SAASrC,KACrDA,EAAShB,EAAcoD,IAExBzB,EAAU2B,aAAaF,EAAW/D,IAAO2B,GAAU,MACnDA,EAASoC,EAAW/D,KAGrB,GACC2B,EAASA,GAAUA,EAAO2B,kBACR,MAAV3B,GAAsC,IAApBA,EAAOuC,UAElC,OAAOvC,CACR,CA4BA,SAASkC,EACRf,EACAG,EACAM,EACAG,GAJD,IAMOxE,EAAM4D,EAAW5D,IACjBF,EAAO8D,EAAW9D,KACpBmF,EAAIZ,EAAc,EAClBa,EAAIb,EAAc,EAClB7B,EAAWuB,EAAYM,GAc3B,GACc,OAAb7B,GACCA,GACAxC,GAAOwC,EAASxC,KAChBF,IAAS0C,EAAS1C,MACc,IAA/B0C,EAAQpB,IAAUpC,GAEpB,OAAOqF,EACD,GAXNG,GACa,MAAZhC,GAAoD,IAA/BA,EAAQpB,IAAUpC,GAAiB,EAAI,GAW7D,KAAOiG,GAAK,GAAKC,EAAInB,EAAY3D,QAAQ,CACxC,GAAI6E,GAAK,EAAG,CAEX,IADAzC,EAAWuB,EAAYkB,KAGU,IAA/BzC,EAAQpB,IAAUpC,IACnBgB,GAAOwC,EAASxC,KAChBF,IAAS0C,EAAS1C,KAElB,OAAOmF,EAERA,GACD,CAEA,GAAIC,EAAInB,EAAY3D,OAAQ,CAE3B,IADAoC,EAAWuB,EAAYmB,KAGU,IAA/B1C,EAAQpB,IAAUpC,IACnBgB,GAAOwC,EAASxC,KAChBF,IAAS0C,EAAS1C,KAElB,OAAOoF,EAERA,GACD,CACD,CAGD,OAAQ,CACT,CHvbA,SAASC,EAASC,EAAOpF,EAAKqF,GACd,MAAXrF,EAAI,GACPoF,EAAME,YAAYtF,EAAc,MAATqF,EAAgB,GAAKA,GAE5CD,EAAMpF,GADa,MAATqF,EACG,GACa,iBAATA,GAAqBlG,EAAmBoG,KAAKvF,GACjDqF,EAEAA,EAAQ,IAEvB,CAuBO,SAASC,EAAYE,EAAKC,EAAMJ,EAAOK,EAAUjC,GACvD,IAAIkC,EAEJC,EAAG,GAAa,UAATH,EACN,GAAoB,iBAATJ,EACVG,EAAIJ,MAAMS,QAAUR,MACd,CAKN,GAJuB,iBAAZK,IACVF,EAAIJ,MAAMS,QAAUH,EAAW,IAG5BA,EACH,IAAKD,KAAQC,EACNL,GAASI,KAAQJ,GACtBF,EAASK,EAAIJ,MAAOK,EAAM,IAK7B,GAAIJ,EACH,IAAKI,KAAQJ,EACPK,GAAYL,EAAMI,KAAUC,EAASD,IACzCN,EAASK,EAAIJ,MAAOK,EAAMJ,EAAMI,GAIpC,MAGQA,GAAY,MAAZA,EAAK,IAA0B,MAAZA,EAAK,GAChCE,EACCF,KAAUA,EAAOA,EAAKK,QAAQ,8BAA+B,OAQ7DL,EAJAA,EAAKM,gBAAiBP,GACb,eAATC,GACS,cAATA,EAEOA,EAAKM,cAAc5H,MAAM,GACrBsH,EAAKtH,MAAM,GAElBqH,EAAGQ,IAAaR,EAAGQ,EAAc,CAAA,GACtCR,EAAGQ,EAAYP,EAAOE,GAAcN,EAEhCA,EACEK,EAQJL,EAAMY,EAAYP,EAASO,GAP3BZ,EAAMY,EAAYtH,EAClB6G,EAAIU,iBACHT,EACAE,EAAa9G,EAAoBD,EACjC+G,IAMFH,EAAIW,oBACHV,EACAE,EAAa9G,EAAoBD,EACjC+G,OAGI,CACN,GAAiB,8BAAblC,EAIHgC,EAAOA,EAAKK,QAAQ,cAAe,KAAKA,QAAQ,SAAU,UACpD,GACE,SAARL,GACQ,UAARA,GACQ,QAARA,GACQ,QAARA,GACQ,QAARA,GAGQ,YAARA,GACQ,YAARA,GACQ,WAARA,GACQ,WAARA,GACQ,QAARA,GACQ,WAARA,GACAA,KAAQD,EAER,IACCA,EAAIC,GAAiB,MAATJ,EAAgB,GAAKA,EAEjC,MAAMO,CACK,CAAV,MAAOQ,GAAG,CAUO,mBAATf,IAES,MAATA,IAA4B,IAAVA,GAA+B,MAAZI,EAAK,GAGpDD,EAAIa,gBAAgBZ,GAFpBD,EAAIc,aAAab,EAAc,WAARA,GAA8B,GAATJ,EAAgB,GAAKA,GAInE,CACD,CAOA,SAASkB,EAAiBZ,GAMzB,OAAiBS,SAAAA,GAChB,GAAI5E,KAAIwE,EAAa,CACpB,IAAMQ,EAAehF,KAAIwE,EAAYI,EAAEtG,KAAO6F,GAC9C,GAAqB,MAAjBS,EAAEK,EACLL,EAAEK,EAAc9H,SAKNyH,GAAAA,EAAEK,EAAcD,EAAaP,EACvC,OAED,OAAOO,EAAapI,EAAQsI,MAAQtI,EAAQsI,MAAMN,GAAKA,EACxD,CACD,CACD,CI5IgB,SAAArD,EACfK,EACAb,EACAC,EACAgB,EACAC,EACAC,EACAhB,EACAD,EACAkB,EACAhB,GAVe,IAaXgE,EAkBE3E,EAAG4E,EAAOC,EAAUC,EAAUC,EAAUC,EACxCC,EACEC,EAMFC,EACAC,EAyGOtI,EA4BPuI,EACHC,EASSxI,EA6BNuE,EAtMLkE,EAAUhF,EAASzC,KAIpB,QAA6BS,IAAzBgC,EAAStB,YAA2B,OAAW,KF9CtB,IEiDzBuB,EAAQpB,MACXuC,KFpD0B,GEoDTnB,EAAQpB,KAEzBsC,EAAoB,CADpBjB,EAASF,EAAQzB,IAAQ0B,EAAQ1B,OAI7B6F,EAAMvI,EAAOyC,MAAS8F,EAAIpE,GAE/BiF,EAAO,GAAsB,mBAAXD,EACjB,IAkEC,GAhEIN,EAAW1E,EAAS/C,MAClB0H,EACL,cAAeK,GAAWA,EAAQE,UAAUC,OAKzCP,GADJR,EAAMY,EAAQI,cACQnE,EAAcmD,EAAG3F,KACnCoG,EAAmBT,EACpBQ,EACCA,EAAS3H,MAAM6F,MACfsB,EAAG/F,GACJ4C,EAGChB,EAAQxB,IAEXgG,GADAhF,EAAIO,EAAQvB,IAAcwB,EAAQxB,KACNJ,GAAwBoB,EAAC4F,KAGjDV,EAEH3E,EAAQvB,IAAcgB,EAAI,IAAIuF,EAAQN,EAAUG,IAGhD7E,EAAQvB,IAAcgB,EAAI,IAAIV,EAC7B2F,EACAG,GAEDpF,EAAEf,YAAcsG,EAChBvF,EAAE0F,OAASG,GAERV,GAAUA,EAASW,IAAI9F,GAE3BA,EAAExC,MAAQyH,EACLjF,EAAE+F,QAAO/F,EAAE+F,MAAQ,CAAE,GAC1B/F,EAAET,QAAU6F,EACZpF,EAACgB,IAAkBQ,EACnBoD,EAAQ5E,EAACjB,KAAU,EACnBiB,EAACgG,IAAoB,GACrBhG,EAACiG,IAAmB,IAIjBf,GAAoC,MAAhBlF,EAACkG,MACxBlG,EAACkG,IAAclG,EAAE+F,OAGdb,GAAwD,MAApCK,EAAQY,2BAC3BnG,EAACkG,KAAelG,EAAE+F,QACrB/F,EAACkG,IAAc5I,EAAO,CAAA,EAAI0C,EAACkG,MAG5B5I,EACC0C,EAACkG,IACDX,EAAQY,yBAAyBlB,EAAUjF,EAACkG,OAI9CrB,EAAW7E,EAAExC,MACbsH,EAAW9E,EAAE+F,MACb/F,EAACd,IAAUqB,EAGPqE,EAEFM,GACoC,MAApCK,EAAQY,0BACgB,MAAxBnG,EAAEoG,oBAEFpG,EAAEoG,qBAGClB,GAA2C,MAAvBlF,EAAEqG,mBACzBrG,EAACgG,IAAkB/F,KAAKD,EAAEqG,uBAErB,CAUN,GARCnB,GACoC,MAApCK,EAAQY,0BACRlB,IAAaJ,GACkB,MAA/B7E,EAAEsG,2BAEFtG,EAAEsG,0BAA0BrB,EAAUG,IAIrCpF,EAAClB,MAC2B,MAA3BkB,EAAEuG,wBAKG,IAJNvG,EAAEuG,sBACDtB,EACAjF,EAACkG,IACDd,IAED7E,EAAQrB,MAAesB,EAAQtB,KAC/B,CAkBD,IAhBIqB,EAAQrB,MAAesB,EAAQtB,MAKlCc,EAAExC,MAAQyH,EACVjF,EAAE+F,MAAQ/F,EAACkG,IACXlG,EAACjB,KAAU,GAGZwB,EAAQzB,IAAQ0B,EAAQ1B,IACxByB,EAAQ5B,IAAa6B,EAAQ7B,IAC7B4B,EAAQ5B,IAAW6H,KAAK,SAAA9H,GACnBA,IAAOA,EAAKE,GAAW2B,EAC5B,GAESzD,EAAI,EAAGA,EAAIkD,EAACiG,IAAiB7H,OAAQtB,IAC7CkD,EAACgG,IAAkB/F,KAAKD,EAACiG,IAAiBnJ,IAE3CkD,EAACiG,IAAmB,GAEhBjG,EAACgG,IAAkB5H,QACtBsC,EAAYT,KAAKD,GAGlB,MAAMwF,CACP,CAE6B,MAAzBxF,EAAEyG,qBACLzG,EAAEyG,oBAAoBxB,EAAUjF,EAACkG,IAAad,GAG3CF,GAA4C,MAAxBlF,EAAE0G,oBACzB1G,EAACgG,IAAkB/F,KAAK,WACvBD,EAAE0G,mBAAmB7B,EAAUC,EAAUC,EAC1C,EAEF,CASA,GAPA/E,EAAET,QAAU6F,EACZpF,EAAExC,MAAQyH,EACVjF,EAACc,IAAcM,EACfpB,EAAClB,KAAU,EAEPuG,EAAajJ,EAAO+D,IACvBmF,EAAQ,EACLJ,EAAkB,CAQrB,IAPAlF,EAAE+F,MAAQ/F,EAACkG,IACXlG,EAACjB,KAAU,EAEPsG,GAAYA,EAAW9E,GAE3BoE,EAAM3E,EAAE0F,OAAO1F,EAAExC,MAAOwC,EAAE+F,MAAO/F,EAAET,SAE1BzC,EAAI,EAAGA,EAAIkD,EAACiG,IAAiB7H,OAAQtB,IAC7CkD,EAACgG,IAAkB/F,KAAKD,EAACiG,IAAiBnJ,IAE3CkD,EAACiG,IAAmB,EACrB,MACC,GACCjG,EAACjB,KAAU,EACPsG,GAAYA,EAAW9E,GAE3BoE,EAAM3E,EAAE0F,OAAO1F,EAAExC,MAAOwC,EAAE+F,MAAO/F,EAAET,SAGnCS,EAAE+F,MAAQ/F,EAACkG,UACHlG,EAACjB,OAAauG,EAAQ,IAIhCtF,EAAE+F,MAAQ/F,EAACkG,IAEc,MAArBlG,EAAE2G,kBACLnF,EAAgBlE,EAAOA,EAAO,CAAA,EAAIkE,GAAgBxB,EAAE2G,oBAGjDzB,IAAqBN,GAAsC,MAA7B5E,EAAE4G,0BACnC7B,EAAW/E,EAAE4G,wBAAwB/B,EAAUC,IAOhD3D,EACCC,EACAhE,EAJGiE,EADI,MAAPsD,GAAeA,EAAI7G,OAASuB,GAAuB,MAAXsF,EAAI3G,IACL2G,EAAInH,MAAMO,SAAW4G,GAIpCtD,EAAe,CAACA,GACxCd,EACAC,EACAgB,EACAC,EACAC,EACAhB,EACAD,EACAkB,EACAhB,GAGDX,EAAEF,KAAOS,EAAQzB,IAGjByB,EAAQnB,MF5Pe,IE8PnBY,EAACgG,IAAkB5H,QACtBsC,EAAYT,KAAKD,GAGdgF,IACHhF,EAAC4F,IAAiB5F,EAACpB,GAAwB,KAoB7C,CAlBE,MAAOwF,GAGR,GAFA7D,EAAQrB,IAAa,KAEjByC,GAAoC,MAArBD,EAA2B,CAK7C,IAJAnB,EAAQnB,KAAWuC,EAChBkF,IFjRuB,IEoRnBpG,GAA8B,IAApBA,EAAOuC,UAAkBvC,EAAO2B,aAChD3B,EAASA,EAAO2B,YAEjBV,EAAkBA,EAAkBoF,QAAQrG,IAAW,KACvDF,EAAQzB,IAAQ2B,CACjB,MACCF,EAAQzB,IAAQ0B,EAAQ1B,IACxByB,EAAQ5B,IAAa6B,EAAQ7B,IAE9BvC,EAAO0C,IAAasF,EAAG7D,EAAUC,EAClC,MAEqB,MAArBkB,GACAnB,EAAQrB,MAAesB,EAAQtB,KAE/BqB,EAAQ5B,IAAa6B,EAAQ7B,IAC7B4B,EAAQzB,IAAQ0B,EAAQ1B,KAExByB,EAAQzB,IAAQiI,EACfvG,EAAQ1B,IACRyB,EACAC,EACAgB,EACAC,EACAC,EACAhB,EACAiB,EACAhB,IAIGgE,EAAMvI,EAAQ4K,SAASrC,EAAIpE,EACjC,CAOgB,SAAAW,EAAWR,EAAauG,EAAMtG,GAC7CsG,EAAIlI,SAAYR,EAEhB,IAAK,IAAIzB,EAAI,EAAGA,EAAI6D,EAASvC,OAAQtB,IACpCoF,EAASvB,EAAS7D,GAAI6D,IAAW7D,GAAI6D,IAAW7D,IAG7CV,EAAO4C,KAAU5C,EAAO4C,IAASiI,EAAMvG,GAE3CA,EAAY8F,KAAK,SAAAxG,GAChB,IAECU,EAAcV,EAACgG,IACfhG,EAACgG,IAAoB,GACrBtF,EAAY8F,KAAK,SAAAU,GAEhBA,EAAG7I,KAAK2B,EACT,EAGD,CAFE,MAAOoE,GACRhI,EAAO0C,IAAasF,EAAGpE,EAACd,IACzB,CACD,EACD,CAiBA,SAAS6H,EACRvD,EACAjD,EACAC,EACAgB,EACAC,EACAC,EACAhB,EACAiB,EACAhB,GATD,IAeK7D,EAEAqK,EAEAC,EAEAC,EACAhE,EACAiE,EACAC,EAbA1C,EAAWrE,EAAShD,MACpByH,EAAW1E,EAAS/C,MACpBwF,EAAkCzC,EAASzC,KAmB/C,GALiB,QAAbkF,EAAoBvB,EAAY,6BACd,SAAbuB,EACRvB,EAAY,qCACHA,IAAWA,EAAY,gCAER,MAArBC,EACH,IAAK5E,EAAI,EAAGA,EAAI4E,EAAkBtD,OAAQtB,IAMzC,IALAuG,EAAQ3B,EAAkB5E,KAOzB,iBAAkBuG,KAAYL,IAC7BA,EAAWK,EAAMmE,YAAcxE,EAA8B,IAAnBK,EAAML,UAChD,CACDQ,EAAMH,EACN3B,EAAkB5E,GAAK,KACvB,KACD,CAIF,GAAW,MAAP0G,EAAa,CAChB,GAAiB,OAAbR,EACH,OAAOyE,SAASC,eAAezC,GAGhCzB,EAAMiE,SAASE,gBACdlG,EACAuB,EACAiC,EAAS2C,IAAM3C,GAKZtD,IACCvF,EAAOyL,KACVzL,EAAOyL,IAAoBtH,EAAUmB,GACtCC,GAAc,GAGfD,EAAoB,IACrB,CAEA,GAAiB,OAAbsB,EAEC6B,IAAaI,GAActD,GAAe6B,EAAIsE,OAAS7C,IAC1DzB,EAAIsE,KAAO7C,OAEN,CASN,GAPAvD,EAAoBA,GAAqBvF,EAAMkC,KAAKmF,EAAIuE,YAExDlD,EAAWrE,EAAShD,OAASP,GAKxB0E,GAAoC,MAArBD,EAEnB,IADAmD,EAAW,CAAE,EACR/H,EAAI,EAAGA,EAAI0G,EAAIwE,WAAW5J,OAAQtB,IAEtC+H,GADAxB,EAAQG,EAAIwE,WAAWlL,IACR2G,MAAQJ,EAAMA,MAI/B,IAAKvG,KAAK+H,EAET,GADAxB,EAAQwB,EAAS/H,GACR,YAALA,QACG,GAAS,2BAALA,EACVsK,EAAU/D,OACA,KAAEvG,KAAKmI,GAAW,CAC5B,GACO,SAALnI,GAAgB,iBAAkBmI,GAC7B,WAALnI,GAAkB,mBAAoBmI,EAEvC,SAED3B,EAAYE,EAAK1G,EAAG,KAAMuG,EAAO5B,EAClC,CAKD,IAAK3E,KAAKmI,EACT5B,EAAQ4B,EAASnI,GACR,YAALA,EACHuK,EAAchE,EACC,2BAALvG,EACVqK,EAAU9D,EACK,SAALvG,EACVwK,EAAajE,EACE,WAALvG,EACVyK,EAAUlE,EAER1B,GAA+B,mBAAT0B,GACxBwB,EAAS/H,KAAOuG,GAEhBC,EAAYE,EAAK1G,EAAGuG,EAAOwB,EAAS/H,GAAI2E,GAK1C,GAAI0F,EAGDxF,GACCyF,IACAD,EAAOc,SAAYb,EAAOa,QAC1Bd,EAAOc,SAAYzE,EAAI0E,aAEzB1E,EAAI0E,UAAYf,EAAOc,QAGxB1H,EAAQ5B,IAAa,QAuBrB,GArBIyI,IAAS5D,EAAI0E,UAAY,IAE7B/G,EACCqC,EACApG,EAAQiK,GAAeA,EAAc,CAACA,GACtC9G,EACAC,EACAgB,EACa,kBAAbwB,EACG,+BACAvB,EACHC,EACAhB,EACAgB,EACGA,EAAkB,GAClBlB,EAAQ7B,KAAcc,EAAce,EAAU,GACjDmB,EACAhB,GAIwB,MAArBe,EACH,IAAK5E,EAAI4E,EAAkBtD,OAAQtB,KAClCW,EAAWiE,EAAkB5E,IAM3B6E,IACJ7E,EAAI,QACa,aAAbkG,GAAyC,MAAdsE,EAC9B9D,EAAIa,gBAAgB,cAEL9F,IAAf+I,IAKCA,IAAe9D,EAAI1G,IACL,aAAbkG,IAA4BsE,GAIf,WAAbtE,GAAyBsE,IAAezC,EAAS/H,KAEnDwG,EAAYE,EAAK1G,EAAGwK,EAAYzC,EAAS/H,GAAI2E,GAG9C3E,EAAI,eACYyB,IAAZgJ,GAAyBA,IAAY/D,EAAI1G,IAC5CwG,EAAYE,EAAK1G,EAAGyK,EAAS1C,EAAS/H,GAAI2E,GAG7C,CAEA,OAAO+B,CACR,CAQgB,SAAAtB,EAASjE,EAAKoF,EAAO3E,GACpC,IACC,GAAkB,mBAAPT,EAAmB,CAC7B,IAAIkK,EAAuC,mBAAhBlK,EAAGmB,IAC1B+I,GAEHlK,EAAGmB,MAGC+I,GAA0B,MAAT9E,IAIrBpF,EAAGmB,IAAYnB,EAAIoF,GAErB,MAAOpF,EAAImK,QAAU/E,CAGtB,CAFE,MAAOe,GACRhI,EAAO0C,IAAasF,EAAG1F,EACxB,CACD,CASgB,SAAAkE,EAAQlE,EAAOmE,EAAawF,GAA5B,IACXC,EAsBMxL,EAbV,GARIV,EAAQwG,SAASxG,EAAQwG,QAAQlE,IAEhC4J,EAAI5J,EAAMT,OACTqK,EAAEF,SAAWE,EAAEF,UAAY1J,EAAKI,KACpCoD,EAASoG,EAAG,KAAMzF,IAIU,OAAzByF,EAAI5J,EAAKM,KAAsB,CACnC,GAAIsJ,EAAEC,qBACL,IACCD,EAAEC,sBAGH,CAFE,MAAOnE,GACRhI,EAAO0C,IAAasF,EAAGvB,EACxB,CAGDyF,EAAExI,KAAOwI,EAACxH,IAAc,IACzB,CAEA,GAAKwH,EAAI5J,EAAKC,IACb,IAAS7B,EAAI,EAAGA,EAAIwL,EAAElK,OAAQtB,IACzBwL,EAAExL,IACL8F,EACC0F,EAAExL,GACF+F,EACAwF,GAAmC,mBAAd3J,EAAMZ,MAM1BuK,GACJ5K,EAAWiB,EAAKI,KAKjBJ,EAAKM,IAAcN,EAAKE,GAAWF,EAAKI,IAAQJ,EAAKK,SAAYR,CAClE,CAGA,SAASsH,EAASrI,EAAOuI,EAAOxG,GAC/B,OAAOC,KAAKP,YAAYzB,EAAO+B,EAChC,UCpnBgBmG,EAAOhH,EAAO0C,EAAWoH,OAMpC7G,EAOAnB,EAQAE,EACHC,EArBGvE,EAAOwC,IAAQxC,EAAOwC,GAAOF,EAAO0C,GAYpCZ,GAPAmB,EAAoC,mBAAf6G,GAQtB,KACCA,GAAeA,EAAW7J,KAAeyC,EAASzC,IAMlD+B,EAAc,GACjBC,EAAW,GACZI,EACCK,EAPD1C,IAAWiD,GAAe6G,GAAgBpH,GAASzC,IAClDd,EAAcwB,EAAU,KAAM,CAACX,IAU/B8B,GAAYvD,EACZA,EACAmE,EAAUH,cACTU,GAAe6G,EACb,CAACA,GACDhI,EACC,KACAY,EAAUqH,WACTtM,EAAMkC,KAAK+C,EAAU2G,YACrB,KACLrH,GACCiB,GAAe6G,EACbA,EACAhI,EACCA,EAAQ1B,IACRsC,EAAUqH,WACd9G,EACAhB,GAIDO,EAAWR,EAAahC,EAAOiC,EAChC,CTpCaxE,EAAQe,EAAUf,MChBzBC,EAAU,CACf0C,ISHe,SAAY4J,EAAOhK,EAAO8B,EAAUmI,GAQnD,IANA,IAAIrI,EAEHsI,EAEAC,EAEOnK,EAAQA,EAAKE,IACpB,IAAK0B,EAAY5B,EAAKM,OAAiBsB,EAAS1B,GAC/C,IAcC,IAbAgK,EAAOtI,EAAUrB,cAE4B,MAAjC2J,EAAKE,2BAChBxI,EAAUyI,SAASH,EAAKE,yBAAyBJ,IACjDG,EAAUvI,EAASvB,KAGe,MAA/BuB,EAAU0I,oBACb1I,EAAU0I,kBAAkBN,EAAOC,GAAa,CAAE,GAClDE,EAAUvI,EAASvB,KAIhB8J,EACH,OAAQvI,EAASsF,IAAiBtF,CAIpC,CAFE,MAAO8D,GACRsE,EAAQtE,CACT,CAIF,MAAMsE,CACP,GRxCIrM,EAAU,EAgGDC,EAAiB,SAAAoC,GAAK,OACzB,MAATA,GAAsCH,MAArBG,EAAMO,WAAwB,ECzEhDK,EAAcmG,UAAUsD,SAAW,SAAUE,EAAQC,GAEpD,IAAIC,EAEHA,EADsB,MAAnB3J,KAAI0G,KAAuB1G,KAAI0G,MAAgB1G,KAAKuG,MACnDvG,KAAI0G,IAEJ1G,KAAI0G,IAAc5I,EAAO,CAAE,EAAEkC,KAAKuG,OAGlB,mBAAVkD,IAGVA,EAASA,EAAO3L,EAAO,GAAI6L,GAAI3J,KAAKhC,QAGjCyL,GACH3L,EAAO6L,EAAGF,GAIG,MAAVA,GAEAzJ,KAAIN,MACHgK,GACH1J,KAAIyG,IAAiBhG,KAAKiJ,GAE3BnJ,EAAcP,MAEhB,EAQAF,EAAcmG,UAAU2D,YAAc,SAAUF,GAC3C1J,KAAIN,MAIPM,KAAIV,KAAU,EACVoK,GAAU1J,KAAIwG,IAAkB/F,KAAKiJ,GACzCnJ,EAAcP,MAEhB,EAYAF,EAAcmG,UAAUC,OAASrG,EA8F7B9C,EAAgB,GAadE,EACa,mBAAX4M,QACJA,QAAQ5D,UAAU6D,KAAKC,KAAKF,QAAQG,WACpCC,WAuBE/M,EAAY,SAACgN,EAAGC,GAAM,OAAAD,EAACxK,IAAAL,IAAiB8K,EAACzK,IAAAL,GAAc,EAuB7DqB,EAAOC,IAAkB,ECtNrBxD,EAAa,EAmJXC,EAAa2H,GAAiB,GAC9B1H,EAAoB0H,GAAiB,GC5KhCzH,EAAI,qCIoEC,SAAA8M,EAAQlL,EAAO0C,GAC9BsE,EAAOhH,EAAO0C,EAAWwI,EAC1B,2CPcO,WACN,MAAO,CAAExB,QAAS,KACnB,4CS5EO,SAAsB1J,EAAOlB,EAAOO,GAApC,IAELC,EACAC,EACAnB,EAEGwB,EALAJ,EAAkBZ,EAAO,CAAE,EAAEoB,EAAMlB,OAWvC,IAAKV,KAJD4B,EAAMZ,MAAQY,EAAMZ,KAAKQ,eAC5BA,EAAeI,EAAMZ,KAAKQ,cAGjBd,EACA,OAALV,EAAYkB,EAAMR,EAAMV,GACd,OAALA,EAAYmB,EAAMT,EAAMV,GAEhCoB,EAAgBpB,QADKyB,IAAbf,EAAMV,SAAqCyB,IAAjBD,EACbA,EAAaxB,GAEbU,EAAMV,GAS7B,OALIqB,UAAUC,OAAS,IACtBF,EAAgBH,SACfI,UAAUC,OAAS,EAAIjC,EAAMkC,KAAKF,UAAW,GAAKJ,GAG7CS,EACNE,EAAMZ,KACNI,EACAF,GAAOU,EAAMV,IACbC,GAAOS,EAAMT,IACb,KAEF,yBN1C8B4L,EAAcC,GAG3C,IAAMvK,EAAU,CACfP,IAHD8K,EAAY,OAAShN,IAIpB8B,GAAeiL,EAEfE,SAAQA,SAACvM,EAAOwM,GAIf,OAAOxM,EAAMO,SAASiM,EACvB,EAEAC,SAAQ,SAACzM,GAAD,IAGF0M,EACAC,EA8BL,OAjCK3K,KAAKmH,kBAELuD,EAAO,IAAIE,KACXD,EAAM,IACNL,GAAatK,KAEjBA,KAAKmH,gBAAkB,WAAM,OAAAwD,CAAG,EAEhC3K,KAAK+I,qBAAuB,WAC3B2B,EAAO,IACR,EAEA1K,KAAK+G,sBAAwB,SAAU8D,GAClC7K,KAAKhC,MAAM6F,QAAUgH,EAAOhH,OAC/B6G,EAAKI,QAAQ,SAAAtK,GACZA,EAAClB,KAAU,EACXiB,EAAcC,EACf,EAEF,EAEAR,KAAKsG,IAAM,SAAA9F,GACVkK,EAAKK,IAAIvK,GACT,IAAIwK,EAAMxK,EAAEuI,qBACZvI,EAAEuI,qBAAuB,WACpB2B,GACHA,EAAKO,OAAOzK,GAETwK,GAAKA,EAAInM,KAAK2B,EACnB,CACD,GAGMxC,EAAMO,QACd,GASD,OAAQwB,EAAQ0K,SAAQrL,GAAeW,EAAQwK,SAASpE,YACvDpG,CACF,eEuSgB,SAAAmL,EAAa3M,EAAU4M,GAUtC,OATAA,EAAMA,GAAO,GACG,MAAZ5M,GAAuC,kBAAZA,IACpBX,EAAQW,GAClBA,EAASyI,KAAK,SAAA3G,GACb6K,EAAa7K,EAAO8K,EACrB,GAEAA,EAAI1K,KAAKlC,IAEH4M,CACR,oBKjXWC,OAAS,IAAKA,OAAOC,QAAUC,EACrCC,KAAKD,OAASA"}