export { render, hydrate } from './render';
export {
	createElement,
	createElement as h,
	Fragment,
	createRef,
	isValidElement
} from './create-element';
export { BaseComponent as Component } from './component';
export { cloneElement } from './clone-element';
export { createContext } from './create-context';
export { toChildArray } from './diff/children';
export { default as options } from './options';
