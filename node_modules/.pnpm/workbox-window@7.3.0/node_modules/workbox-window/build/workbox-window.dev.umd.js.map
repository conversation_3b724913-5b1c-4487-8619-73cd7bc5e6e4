{"version": 3, "file": "workbox-window.dev.umd.js", "sources": ["../_version.js", "../messageSW.js", "../../workbox-core/_version.js", "../../workbox-core/_private/Deferred.js", "../../workbox-core/_private/dontWaitFor.js", "../../workbox-core/_private/logger.js", "../utils/WorkboxEventTarget.js", "../utils/urlsMatch.js", "../utils/WorkboxEvent.js", "../Workbox.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:window:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * Sends a data object to a service worker via `postMessage` and resolves with\n * a response (if any).\n *\n * A response can be set in a message handler in the service worker by\n * calling `event.ports[0].postMessage(...)`, which will resolve the promise\n * returned by `messageSW()`. If no response is set, the promise will not\n * resolve.\n *\n * @param {ServiceWorker} sw The service worker to send the message to.\n * @param {Object} data An object to send to the service worker.\n * @return {Promise<Object|undefined>}\n * @memberof workbox-window\n */\n// Better not change type of data.\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction messageSW(sw, data) {\n    return new Promise((resolve) => {\n        const messageChannel = new MessageChannel();\n        messageChannel.port1.onmessage = (event) => {\n            resolve(event.data);\n        };\n        sw.postMessage(data, [messageChannel.port2]);\n    });\n}\nexport { messageSW };\n", "\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:core:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The Deferred class composes Promises in a way that allows for them to be\n * resolved or rejected from outside the constructor. In most cases promises\n * should be used directly, but Deferreds can be necessary when the logic to\n * resolve a promise must be separate.\n *\n * @private\n */\nclass Deferred {\n    /**\n     * Creates a promise and exposes its resolve and reject functions as methods.\n     */\n    constructor() {\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n}\nexport { Deferred };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A helper function that prevents a promise from being flagged as unused.\n *\n * @private\n **/\nexport function dontWaitFor(promise) {\n    // Effective no-op.\n    void promise.then(() => { });\n}\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst logger = (process.env.NODE_ENV === 'production'\n    ? null\n    : (() => {\n        // Don't overwrite this value if it's already set.\n        // See https://github.com/GoogleChrome/workbox/pull/2284#issuecomment-560470923\n        if (!('__WB_DISABLE_DEV_LOGS' in globalThis)) {\n            self.__WB_DISABLE_DEV_LOGS = false;\n        }\n        let inGroup = false;\n        const methodToColorMap = {\n            debug: `#7f8c8d`,\n            log: `#2ecc71`,\n            warn: `#f39c12`,\n            error: `#c0392b`,\n            groupCollapsed: `#3498db`,\n            groupEnd: null, // No colored prefix on groupEnd\n        };\n        const print = function (method, args) {\n            if (self.__WB_DISABLE_DEV_LOGS) {\n                return;\n            }\n            if (method === 'groupCollapsed') {\n                // Safari doesn't print all console.groupCollapsed() arguments:\n                // https://bugs.webkit.org/show_bug.cgi?id=182754\n                if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n                    console[method](...args);\n                    return;\n                }\n            }\n            const styles = [\n                `background: ${methodToColorMap[method]}`,\n                `border-radius: 0.5em`,\n                `color: white`,\n                `font-weight: bold`,\n                `padding: 2px 0.5em`,\n            ];\n            // When in a group, the workbox prefix is not displayed.\n            const logPrefix = inGroup ? [] : ['%cworkbox', styles.join(';')];\n            console[method](...logPrefix, ...args);\n            if (method === 'groupCollapsed') {\n                inGroup = true;\n            }\n            if (method === 'groupEnd') {\n                inGroup = false;\n            }\n        };\n        // eslint-disable-next-line @typescript-eslint/ban-types\n        const api = {};\n        const loggerMethods = Object.keys(methodToColorMap);\n        for (const key of loggerMethods) {\n            const method = key;\n            api[method] = (...args) => {\n                print(method, args);\n            };\n        }\n        return api;\n    })());\nexport { logger };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\n/**\n * A minimal `EventTarget` shim.\n * This is necessary because not all browsers support constructable\n * `EventTarget`, so using a real `EventTarget` will error.\n * @private\n */\nexport class WorkboxEventTarget {\n    constructor() {\n        this._eventListenerRegistry = new Map();\n    }\n    /**\n     * @param {string} type\n     * @param {Function} listener\n     * @private\n     */\n    addEventListener(type, listener) {\n        const foo = this._getEventListenersByType(type);\n        foo.add(listener);\n    }\n    /**\n     * @param {string} type\n     * @param {Function} listener\n     * @private\n     */\n    removeEventListener(type, listener) {\n        this._getEventListenersByType(type).delete(listener);\n    }\n    /**\n     * @param {Object} event\n     * @private\n     */\n    dispatchEvent(event) {\n        event.target = this;\n        const listeners = this._getEventListenersByType(event.type);\n        for (const listener of listeners) {\n            listener(event);\n        }\n    }\n    /**\n     * Returns a Set of listeners associated with the passed event type.\n     * If no handlers have been registered, an empty Set is returned.\n     *\n     * @param {string} type The event type.\n     * @return {Set<ListenerCallback>} An array of handler functions.\n     * @private\n     */\n    _getEventListenersByType(type) {\n        if (!this._eventListenerRegistry.has(type)) {\n            this._eventListenerRegistry.set(type, new Set());\n        }\n        return this._eventListenerRegistry.get(type);\n    }\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Returns true if two URLs have the same `.href` property. The URLS can be\n * relative, and if they are the current location href is used to resolve URLs.\n *\n * @private\n * @param {string} url1\n * @param {string} url2\n * @return {boolean}\n */\nexport function urlsMatch(url1, url2) {\n    const { href } = location;\n    return new URL(url1, href).href === new URL(url2, href).href;\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A minimal `Event` subclass shim.\n * This doesn't *actually* subclass `Event` because not all browsers support\n * constructable `EventTarget`, and using a real `Event` will error.\n * @private\n */\nexport class WorkboxEvent {\n    constructor(type, props) {\n        this.type = type;\n        Object.assign(this, props);\n    }\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { dontWaitFor } from 'workbox-core/_private/dontWaitFor.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { messageSW } from './messageSW.js';\nimport { WorkboxEventTarget } from './utils/WorkboxEventTarget.js';\nimport { urlsMatch } from './utils/urlsMatch.js';\nimport { WorkboxEvent } from './utils/WorkboxEvent.js';\nimport './_version.js';\n// The time a SW must be in the waiting phase before we can conclude\n// `skipWaiting()` wasn't called. This 200 amount wasn't scientifically\n// chosen, but it seems to avoid false positives in my testing.\nconst WAITING_TIMEOUT_DURATION = 200;\n// The amount of time after a registration that we can reasonably conclude\n// that the registration didn't trigger an update.\nconst REGISTRATION_TIMEOUT_DURATION = 60000;\n// The de facto standard message that a service worker should be listening for\n// to trigger a call to skipWaiting().\nconst SKIP_WAITING_MESSAGE = { type: 'SKIP_WAITING' };\n/**\n * A class to aid in handling service worker registration, updates, and\n * reacting to service worker lifecycle events.\n *\n * @fires {@link workbox-window.Workbox#message}\n * @fires {@link workbox-window.Workbox#installed}\n * @fires {@link workbox-window.Workbox#waiting}\n * @fires {@link workbox-window.Workbox#controlling}\n * @fires {@link workbox-window.Workbox#activated}\n * @fires {@link workbox-window.Workbox#redundant}\n * @memberof workbox-window\n */\nclass Workbox extends WorkboxEventTarget {\n    /**\n     * Creates a new Workbox instance with a script URL and service worker\n     * options. The script URL and options are the same as those used when\n     * calling [navigator.serviceWorker.register(scriptURL, options)](https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/register).\n     *\n     * @param {string|TrustedScriptURL} scriptURL The service worker script\n     *     associated with this instance. Using a\n     *     [`TrustedScriptURL`](https://web.dev/trusted-types/) is supported.\n     * @param {Object} [registerOptions] The service worker options associated\n     *     with this instance.\n     */\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    constructor(scriptURL, registerOptions = {}) {\n        super();\n        this._registerOptions = {};\n        this._updateFoundCount = 0;\n        // Deferreds we can resolve later.\n        this._swDeferred = new Deferred();\n        this._activeDeferred = new Deferred();\n        this._controllingDeferred = new Deferred();\n        this._registrationTime = 0;\n        this._ownSWs = new Set();\n        /**\n         * @private\n         */\n        this._onUpdateFound = () => {\n            // `this._registration` will never be `undefined` after an update is found.\n            const registration = this._registration;\n            const installingSW = registration.installing;\n            // If the script URL passed to `navigator.serviceWorker.register()` is\n            // different from the current controlling SW's script URL, we know any\n            // successful registration calls will trigger an `updatefound` event.\n            // But if the registered script URL is the same as the current controlling\n            // SW's script URL, we'll only get an `updatefound` event if the file\n            // changed since it was last registered. This can be a problem if the user\n            // opens up the same page in a different tab, and that page registers\n            // a SW that triggers an update. It's a problem because this page has no\n            // good way of knowing whether the `updatefound` event came from the SW\n            // script it registered or from a registration attempt made by a newer\n            // version of the page running in another tab.\n            // To minimize the possibility of a false positive, we use the logic here:\n            const updateLikelyTriggeredExternally = \n            // Since we enforce only calling `register()` once, and since we don't\n            // add the `updatefound` event listener until the `register()` call, if\n            // `_updateFoundCount` is > 0 then it means this method has already\n            // been called, thus this SW must be external\n            this._updateFoundCount > 0 ||\n                // If the script URL of the installing SW is different from this\n                // instance's script URL, we know it's definitely not from our\n                // registration.\n                !urlsMatch(installingSW.scriptURL, this._scriptURL.toString()) ||\n                // If all of the above are false, then we use a time-based heuristic:\n                // Any `updatefound` event that occurs long after our registration is\n                // assumed to be external.\n                performance.now() > this._registrationTime + REGISTRATION_TIMEOUT_DURATION\n                ? // If any of the above are not true, we assume the update was\n                    // triggered by this instance.\n                    true\n                : false;\n            if (updateLikelyTriggeredExternally) {\n                this._externalSW = installingSW;\n                registration.removeEventListener('updatefound', this._onUpdateFound);\n            }\n            else {\n                // If the update was not triggered externally we know the installing\n                // SW is the one we registered, so we set it.\n                this._sw = installingSW;\n                this._ownSWs.add(installingSW);\n                this._swDeferred.resolve(installingSW);\n                // The `installing` state isn't something we have a dedicated\n                // callback for, but we do log messages for it in development.\n                if (process.env.NODE_ENV !== 'production') {\n                    if (navigator.serviceWorker.controller) {\n                        logger.log('Updated service worker found. Installing now...');\n                    }\n                    else {\n                        logger.log('Service worker is installing...');\n                    }\n                }\n            }\n            // Increment the `updatefound` count, so future invocations of this\n            // method can be sure they were triggered externally.\n            ++this._updateFoundCount;\n            // Add a `statechange` listener regardless of whether this update was\n            // triggered externally, since we have callbacks for both.\n            installingSW.addEventListener('statechange', this._onStateChange);\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onStateChange = (originalEvent) => {\n            // `this._registration` will never be `undefined` after an update is found.\n            const registration = this._registration;\n            const sw = originalEvent.target;\n            const { state } = sw;\n            const isExternal = sw === this._externalSW;\n            const eventProps = {\n                sw,\n                isExternal,\n                originalEvent,\n            };\n            if (!isExternal && this._isUpdate) {\n                eventProps.isUpdate = true;\n            }\n            this.dispatchEvent(new WorkboxEvent(state, eventProps));\n            if (state === 'installed') {\n                // This timeout is used to ignore cases where the service worker calls\n                // `skipWaiting()` in the install event, thus moving it directly in the\n                // activating state. (Since all service workers *must* go through the\n                // waiting phase, the only way to detect `skipWaiting()` called in the\n                // install event is to observe that the time spent in the waiting phase\n                // is very short.)\n                // NOTE: we don't need separate timeouts for the own and external SWs\n                // since they can't go through these phases at the same time.\n                this._waitingTimeout = self.setTimeout(() => {\n                    // Ensure the SW is still waiting (it may now be redundant).\n                    if (state === 'installed' && registration.waiting === sw) {\n                        this.dispatchEvent(new WorkboxEvent('waiting', eventProps));\n                        if (process.env.NODE_ENV !== 'production') {\n                            if (isExternal) {\n                                logger.warn('An external service worker has installed but is ' +\n                                    'waiting for this client to close before activating...');\n                            }\n                            else {\n                                logger.warn('The service worker has installed but is waiting ' +\n                                    'for existing clients to close before activating...');\n                            }\n                        }\n                    }\n                }, WAITING_TIMEOUT_DURATION);\n            }\n            else if (state === 'activating') {\n                clearTimeout(this._waitingTimeout);\n                if (!isExternal) {\n                    this._activeDeferred.resolve(sw);\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                switch (state) {\n                    case 'installed':\n                        if (isExternal) {\n                            logger.warn('An external service worker has installed. ' +\n                                'You may want to suggest users reload this page.');\n                        }\n                        else {\n                            logger.log('Registered service worker installed.');\n                        }\n                        break;\n                    case 'activated':\n                        if (isExternal) {\n                            logger.warn('An external service worker has activated.');\n                        }\n                        else {\n                            logger.log('Registered service worker activated.');\n                            if (sw !== navigator.serviceWorker.controller) {\n                                logger.warn('The registered service worker is active but ' +\n                                    'not yet controlling the page. Reload or run ' +\n                                    '`clients.claim()` in the service worker.');\n                            }\n                        }\n                        break;\n                    case 'redundant':\n                        if (sw === this._compatibleControllingSW) {\n                            logger.log('Previously controlling service worker now redundant!');\n                        }\n                        else if (!isExternal) {\n                            logger.log('Registered service worker now redundant!');\n                        }\n                        break;\n                }\n            }\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onControllerChange = (originalEvent) => {\n            const sw = this._sw;\n            const isExternal = sw !== navigator.serviceWorker.controller;\n            // Unconditionally dispatch the controlling event, with isExternal set\n            // to distinguish between controller changes due to the initial registration\n            // vs. an update-check or other tab's registration.\n            // See https://github.com/GoogleChrome/workbox/issues/2786\n            this.dispatchEvent(new WorkboxEvent('controlling', {\n                isExternal,\n                originalEvent,\n                sw,\n                isUpdate: this._isUpdate,\n            }));\n            if (!isExternal) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log('Registered service worker now controlling this page.');\n                }\n                this._controllingDeferred.resolve(sw);\n            }\n        };\n        /**\n         * @private\n         * @param {Event} originalEvent\n         */\n        this._onMessage = async (originalEvent) => {\n            // Can't change type 'any' of data.\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            const { data, ports, source } = originalEvent;\n            // Wait until there's an \"own\" service worker. This is used to buffer\n            // `message` events that may be received prior to calling `register()`.\n            await this.getSW();\n            // If the service worker that sent the message is in the list of own\n            // service workers for this instance, dispatch a `message` event.\n            // NOTE: we check for all previously owned service workers rather than\n            // just the current one because some messages (e.g. cache updates) use\n            // a timeout when sent and may be delayed long enough for a service worker\n            // update to be found.\n            if (this._ownSWs.has(source)) {\n                this.dispatchEvent(new WorkboxEvent('message', {\n                    // Can't change type 'any' of data.\n                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                    data,\n                    originalEvent,\n                    ports,\n                    sw: source,\n                }));\n            }\n        };\n        this._scriptURL = scriptURL;\n        this._registerOptions = registerOptions;\n        // Add a message listener immediately since messages received during\n        // page load are buffered only until the DOMContentLoaded event:\n        // https://github.com/GoogleChrome/workbox/issues/2202\n        navigator.serviceWorker.addEventListener('message', this._onMessage);\n    }\n    /**\n     * Registers a service worker for this instances script URL and service\n     * worker options. By default this method delays registration until after\n     * the window has loaded.\n     *\n     * @param {Object} [options]\n     * @param {Function} [options.immediate=false] Setting this to true will\n     *     register the service worker immediately, even if the window has\n     *     not loaded (not recommended).\n     */\n    async register({ immediate = false } = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            if (this._registrationTime) {\n                logger.error('Cannot re-register a Workbox instance after it has ' +\n                    'been registered. Create a new instance instead.');\n                return;\n            }\n        }\n        if (!immediate && document.readyState !== 'complete') {\n            await new Promise((res) => window.addEventListener('load', res));\n        }\n        // Set this flag to true if any service worker was controlling the page\n        // at registration time.\n        this._isUpdate = Boolean(navigator.serviceWorker.controller);\n        // Before registering, attempt to determine if a SW is already controlling\n        // the page, and if that SW script (and version, if specified) matches this\n        // instance's script.\n        this._compatibleControllingSW = this._getControllingSWIfCompatible();\n        this._registration = await this._registerScript();\n        // If we have a compatible controller, store the controller as the \"own\"\n        // SW, resolve active/controlling deferreds and add necessary listeners.\n        if (this._compatibleControllingSW) {\n            this._sw = this._compatibleControllingSW;\n            this._activeDeferred.resolve(this._compatibleControllingSW);\n            this._controllingDeferred.resolve(this._compatibleControllingSW);\n            this._compatibleControllingSW.addEventListener('statechange', this._onStateChange, { once: true });\n        }\n        // If there's a waiting service worker with a matching URL before the\n        // `updatefound` event fires, it likely means that this site is open\n        // in another tab, or the user refreshed the page (and thus the previous\n        // page wasn't fully unloaded before this page started loading).\n        // https://developers.google.com/web/fundamentals/primers/service-workers/lifecycle#waiting\n        const waitingSW = this._registration.waiting;\n        if (waitingSW &&\n            urlsMatch(waitingSW.scriptURL, this._scriptURL.toString())) {\n            // Store the waiting SW as the \"own\" Sw, even if it means overwriting\n            // a compatible controller.\n            this._sw = waitingSW;\n            // Run this in the next microtask, so any code that adds an event\n            // listener after awaiting `register()` will get this event.\n            dontWaitFor(Promise.resolve().then(() => {\n                this.dispatchEvent(new WorkboxEvent('waiting', {\n                    sw: waitingSW,\n                    wasWaitingBeforeRegister: true,\n                }));\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.warn('A service worker was already waiting to activate ' +\n                        'before this script was registered...');\n                }\n            }));\n        }\n        // If an \"own\" SW is already set, resolve the deferred.\n        if (this._sw) {\n            this._swDeferred.resolve(this._sw);\n            this._ownSWs.add(this._sw);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log('Successfully registered service worker.', this._scriptURL.toString());\n            if (navigator.serviceWorker.controller) {\n                if (this._compatibleControllingSW) {\n                    logger.debug('A service worker with the same script URL ' +\n                        'is already controlling this page.');\n                }\n                else {\n                    logger.debug('A service worker with a different script URL is ' +\n                        'currently controlling the page. The browser is now fetching ' +\n                        'the new script now...');\n                }\n            }\n            const currentPageIsOutOfScope = () => {\n                const scopeURL = new URL(this._registerOptions.scope || this._scriptURL.toString(), document.baseURI);\n                const scopeURLBasePath = new URL('./', scopeURL.href).pathname;\n                return !location.pathname.startsWith(scopeURLBasePath);\n            };\n            if (currentPageIsOutOfScope()) {\n                logger.warn('The current page is not in scope for the registered ' +\n                    'service worker. Was this a mistake?');\n            }\n        }\n        this._registration.addEventListener('updatefound', this._onUpdateFound);\n        navigator.serviceWorker.addEventListener('controllerchange', this._onControllerChange);\n        return this._registration;\n    }\n    /**\n     * Checks for updates of the registered service worker.\n     */\n    async update() {\n        if (!this._registration) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error('Cannot update a Workbox instance without ' +\n                    'being registered. Register the Workbox instance first.');\n            }\n            return;\n        }\n        // Try to update registration\n        await this._registration.update();\n    }\n    /**\n     * Resolves to the service worker registered by this instance as soon as it\n     * is active. If a service worker was already controlling at registration\n     * time then it will resolve to that if the script URLs (and optionally\n     * script versions) match, otherwise it will wait until an update is found\n     * and activates.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    get active() {\n        return this._activeDeferred.promise;\n    }\n    /**\n     * Resolves to the service worker registered by this instance as soon as it\n     * is controlling the page. If a service worker was already controlling at\n     * registration time then it will resolve to that if the script URLs (and\n     * optionally script versions) match, otherwise it will wait until an update\n     * is found and starts controlling the page.\n     * Note: the first time a service worker is installed it will active but\n     * not start controlling the page unless `clients.claim()` is called in the\n     * service worker.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    get controlling() {\n        return this._controllingDeferred.promise;\n    }\n    /**\n     * Resolves with a reference to a service worker that matches the script URL\n     * of this instance, as soon as it's available.\n     *\n     * If, at registration time, there's already an active or waiting service\n     * worker with a matching script URL, it will be used (with the waiting\n     * service worker taking precedence over the active service worker if both\n     * match, since the waiting service worker would have been registered more\n     * recently).\n     * If there's no matching active or waiting service worker at registration\n     * time then the promise will not resolve until an update is found and starts\n     * installing, at which point the installing service worker is used.\n     *\n     * @return {Promise<ServiceWorker>}\n     */\n    getSW() {\n        // If `this._sw` is set, resolve with that as we want `getSW()` to\n        // return the correct (new) service worker if an update is found.\n        return this._sw !== undefined\n            ? Promise.resolve(this._sw)\n            : this._swDeferred.promise;\n    }\n    /**\n     * Sends the passed data object to the service worker registered by this\n     * instance (via {@link workbox-window.Workbox#getSW}) and resolves\n     * with a response (if any).\n     *\n     * A response can be set in a message handler in the service worker by\n     * calling `event.ports[0].postMessage(...)`, which will resolve the promise\n     * returned by `messageSW()`. If no response is set, the promise will never\n     * resolve.\n     *\n     * @param {Object} data An object to send to the service worker\n     * @return {Promise<Object>}\n     */\n    // We might be able to change the 'data' type to Record<string, unknown> in the future.\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    async messageSW(data) {\n        const sw = await this.getSW();\n        return messageSW(sw, data);\n    }\n    /**\n     * Sends a `{type: 'SKIP_WAITING'}` message to the service worker that's\n     * currently in the `waiting` state associated with the current registration.\n     *\n     * If there is no current registration or no service worker is `waiting`,\n     * calling this will have no effect.\n     */\n    messageSkipWaiting() {\n        if (this._registration && this._registration.waiting) {\n            void messageSW(this._registration.waiting, SKIP_WAITING_MESSAGE);\n        }\n    }\n    /**\n     * Checks for a service worker already controlling the page and returns\n     * it if its script URL matches.\n     *\n     * @private\n     * @return {ServiceWorker|undefined}\n     */\n    _getControllingSWIfCompatible() {\n        const controller = navigator.serviceWorker.controller;\n        if (controller &&\n            urlsMatch(controller.scriptURL, this._scriptURL.toString())) {\n            return controller;\n        }\n        else {\n            return undefined;\n        }\n    }\n    /**\n     * Registers a service worker for this instances script URL and register\n     * options and tracks the time registration was complete.\n     *\n     * @private\n     */\n    async _registerScript() {\n        try {\n            // this._scriptURL may be a TrustedScriptURL, but there's no support for\n            // passing that to register() in lib.dom right now.\n            // https://github.com/GoogleChrome/workbox/issues/2855\n            const reg = await navigator.serviceWorker.register(this._scriptURL, this._registerOptions);\n            // Keep track of when registration happened, so it can be used in the\n            // `this._onUpdateFound` heuristic. Also use the presence of this\n            // property as a way to see if `.register()` has been called.\n            this._registrationTime = performance.now();\n            return reg;\n        }\n        catch (error) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error(error);\n            }\n            // Re-throw the error.\n            throw error;\n        }\n    }\n}\nexport { Workbox };\n// The jsdoc comments below outline the events this instance may dispatch:\n// -----------------------------------------------------------------------\n/**\n * The `message` event is dispatched any time a `postMessage` is received.\n *\n * @event workbox-window.Workbox#message\n * @type {WorkboxEvent}\n * @property {*} data The `data` property from the original `message` event.\n * @property {Event} originalEvent The original [`message`]{@link https://developer.mozilla.org/en-US/docs/Web/API/MessageEvent}\n *     event.\n * @property {string} type `message`.\n * @property {MessagePort[]} ports The `ports` value from `originalEvent`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `installed` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * {@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw|registered service worker}\n * changes to `installed`.\n *\n * Then can happen either the very first time a service worker is installed,\n * or after an update to the current service worker is found. In the case\n * of an update being found, the event's `isUpdate` property will be `true`.\n *\n * @event workbox-window.Workbox#installed\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `installed`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `waiting` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}\n * changes to `installed` and then doesn't immediately change to `activating`.\n * It may also be dispatched if a service worker with the same\n * [`scriptURL`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/scriptURL}\n * was already waiting when the {@link workbox-window.Workbox#register}\n * method was called.\n *\n * @event workbox-window.Workbox#waiting\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event|undefined} originalEvent The original\n *    [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event, or `undefined` in the case where the service worker was waiting\n *     to before `.register()` was called.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {boolean|undefined} wasWaitingBeforeRegister True if a service worker with\n *     a matching `scriptURL` was already waiting when this `Workbox`\n *     instance called `register()`.\n * @property {string} type `waiting`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `controlling` event is dispatched if a\n * [`controllerchange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/oncontrollerchange}\n * fires on the service worker [container]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer}\n * and the [`scriptURL`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/scriptURL}\n * of the new [controller]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/controller}\n * matches the `scriptURL` of the `Workbox` instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}.\n *\n * @event workbox-window.Workbox#controlling\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`controllerchange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/oncontrollerchange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this service worker was registered.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `controlling`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `activated` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * {@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw|registered service worker}\n * changes to `activated`.\n *\n * @event workbox-window.Workbox#activated\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {boolean|undefined} isExternal True if this event is associated\n *     with an [external service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-window#when_an_unexpected_version_of_the_service_worker_is_found}.\n * @property {string} type `activated`.\n * @property {Workbox} target The `Workbox` instance.\n */\n/**\n * The `redundant` event is dispatched if the state of a\n * {@link workbox-window.Workbox} instance's\n * [registered service worker]{@link https://developers.google.com/web/tools/workbox/modules/workbox-precaching#def-registered-sw}\n * changes to `redundant`.\n *\n * @event workbox-window.Workbox#redundant\n * @type {WorkboxEvent}\n * @property {ServiceWorker} sw The service worker instance.\n * @property {Event} originalEvent The original [`statechange`]{@link https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/onstatechange}\n *     event.\n * @property {boolean|undefined} isUpdate True if a service worker was already\n *     controlling when this `Workbox` instance called `register()`.\n * @property {string} type `redundant`.\n * @property {Workbox} target The `Workbox` instance.\n */\n"], "names": ["self", "_", "e", "messageSW", "sw", "data", "Promise", "resolve", "messageChannel", "MessageChannel", "port1", "onmessage", "event", "postMessage", "port2", "Deferred", "_this", "promise", "reject", "dontWait<PERSON>or", "then", "logger", "globalThis", "__WB_DISABLE_DEV_LOGS", "inGroup", "methodToColorMap", "debug", "log", "warn", "error", "groupCollapsed", "groupEnd", "print", "method", "args", "_console2", "test", "navigator", "userAgent", "_console", "console", "apply", "styles", "logPrefix", "join", "concat", "api", "loggerMethods", "Object", "keys", "_loop", "key", "_loggerMethods", "_i", "_len", "arguments", "length", "Array", "_key", "WorkboxEventTarget", "_eventListenerRegistry", "Map", "_proto", "prototype", "addEventListener", "type", "listener", "foo", "_getEventListenersByType", "add", "removeEventListener", "delete", "dispatchEvent", "target", "listeners", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "value", "has", "set", "Set", "get", "urlsMatch", "url1", "url2", "_location", "location", "href", "URL", "WorkboxEvent", "props", "assign", "_await", "direct", "WAITING_TIMEOUT_DURATION", "_async", "f", "i", "REGISTRATION_TIMEOUT_DURATION", "_empty", "SKIP_WAITING_MESSAGE", "_awaitIgnored", "Workbox", "_WorkboxEventTarget", "scriptURL", "registerOptions", "call", "_registerOptions", "_updateFoundCount", "_swDeferred", "_activeD<PERSON><PERSON>red", "_controlling<PERSON><PERSON><PERSON><PERSON>", "_registrationTime", "_ownSWs", "_onUpdateFound", "registration", "_registration", "installingSW", "installing", "updateLikelyTriggeredExternally", "_scriptURL", "toString", "performance", "now", "_externalSW", "_sw", "serviceWorker", "controller", "_onStateChange", "originalEvent", "state", "isExternal", "eventProps", "_isUpdate", "isUpdate", "_waitingTimeout", "setTimeout", "waiting", "clearTimeout", "_compatibleControllingSW", "_onControllerChange", "_onMessage", "ports", "source", "getSW", "_inherits<PERSON><PERSON>e", "register", "_temp", "_ref", "_ref$immediate", "immediate", "_this2", "process", "_invoke", "document", "readyState", "res", "window", "Boolean", "_getControllingSWIfCompatible", "_registerScript", "_this2$_registerScrip", "once", "waitingSW", "wasWaitingBeforeRegister", "currentPageIsOutOfScope", "scopeURL", "scope", "baseURI", "scopeURLBasePath", "pathname", "startsWith", "update", "_this3", "undefined", "_this4", "messageSkipWaiting", "_this5", "_catch", "reg", "_createClass", "body", "result", "recover"], "mappings": ";;;;;;IACA;IACA,IAAI;IACAA,EAAAA,IAAI,CAAC,sBAAsB,CAAC,IAAIC,CAAC,EAAE,CAAA;IACvC,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAASC,SAASA,CAACC,EAAE,EAAEC,IAAI,EAAE;IACzB,EAAA,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAK;IAC5B,IAAA,IAAMC,cAAc,GAAG,IAAIC,cAAc,EAAE,CAAA;IAC3CD,IAAAA,cAAc,CAACE,KAAK,CAACC,SAAS,GAAG,UAACC,KAAK,EAAK;IACxCL,MAAAA,OAAO,CAACK,KAAK,CAACP,IAAI,CAAC,CAAA;SACtB,CAAA;QACDD,EAAE,CAACS,WAAW,CAACR,IAAI,EAAE,CAACG,cAAc,CAACM,KAAK,CAAC,CAAC,CAAA;IAChD,GAAC,CAAC,CAAA;IACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC/BA;IACA,IAAI;IACAd,EAAAA,IAAI,CAAC,oBAAoB,CAAC,IAAIC,CAAC,EAAE,CAAA;IACrC,CAAC,CACD,OAAOC,CAAC,EAAE;;ICLV;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAPA,IAQMa,QAAQ;IACV;IACJ;IACA;IACI,SAAAA,WAAc;IAAA,EAAA,IAAAC,KAAA,GAAA,IAAA,CAAA;MACV,IAAI,CAACC,OAAO,GAAG,IAAIX,OAAO,CAAC,UAACC,OAAO,EAAEW,MAAM,EAAK;QAC5CF,KAAI,CAACT,OAAO,GAAGA,OAAO,CAAA;QACtBS,KAAI,CAACE,MAAM,GAAGA,MAAM,CAAA;IACxB,GAAC,CAAC,CAAA;IACN,CAAC;;ICzBL;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACO,SAASC,WAAWA,CAACF,OAAO,EAAE;IACjC;IACA,EAAA,KAAKA,OAAO,CAACG,IAAI,CAAC,YAAM,EAAG,CAAC,CAAA;IAChC;;ICfA;IACA;IACA;IACA;IACA;IACA;IAEA,IAAMC,MAAM,GAEL,YAAM;IACL;IACA;IACA,EAAA,IAAI,EAAE,uBAAuB,IAAIC,UAAU,CAAC,EAAE;QAC1CtB,IAAI,CAACuB,qBAAqB,GAAG,KAAK,CAAA;IACtC,GAAA;MACA,IAAIC,OAAO,GAAG,KAAK,CAAA;IACnB,EAAA,IAAMC,gBAAgB,GAAG;IACrBC,IAAAA,KAAK,EAAW,SAAA;IAChBC,IAAAA,GAAG,EAAW,SAAA;IACdC,IAAAA,IAAI,EAAW,SAAA;IACfC,IAAAA,KAAK,EAAW,SAAA;IAChBC,IAAAA,cAAc,EAAW,SAAA;QACzBC,QAAQ,EAAE,IAAI;OACjB,CAAA;MACD,IAAMC,KAAK,GAAG,SAARA,KAAKA,CAAaC,MAAM,EAAEC,IAAI,EAAE;IAAA,IAAA,IAAAC,SAAA,CAAA;QAClC,IAAInC,IAAI,CAACuB,qBAAqB,EAAE;IAC5B,MAAA,OAAA;IACJ,KAAA;QACA,IAAIU,MAAM,KAAK,gBAAgB,EAAE;IAC7B;IACA;UACA,IAAI,gCAAgC,CAACG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,EAAE;IAAA,QAAA,IAAAC,QAAA,CAAA;IAC5D,QAAA,CAAAA,QAAA,GAAAC,OAAO,EAACP,MAAM,CAAC,CAAAQ,KAAA,CAAAF,QAAA,EAAIL,IAAI,CAAC,CAAA;IACxB,QAAA,OAAA;IACJ,OAAA;IACJ,KAAA;IACA,IAAA,IAAMQ,MAAM,GAAG,CAAA,cAAA,GACIjB,gBAAgB,CAACQ,MAAM,CAAC,EAK1C,sBAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,oBAAA,CAAA,CAAA;IACD;IACA,IAAA,IAAMU,SAAS,GAAGnB,OAAO,GAAG,EAAE,GAAG,CAAC,WAAW,EAAEkB,MAAM,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;IAChE,IAAA,CAAAT,SAAA,GAAAK,OAAO,EAACP,MAAM,CAAC,CAAAQ,KAAA,CAAAN,SAAA,EAAIQ,SAAS,CAAAE,MAAA,CAAKX,IAAI,CAAC,CAAA,CAAA;QACtC,IAAID,MAAM,KAAK,gBAAgB,EAAE;IAC7BT,MAAAA,OAAO,GAAG,IAAI,CAAA;IAClB,KAAA;QACA,IAAIS,MAAM,KAAK,UAAU,EAAE;IACvBT,MAAAA,OAAO,GAAG,KAAK,CAAA;IACnB,KAAA;OACH,CAAA;IACD;MACA,IAAMsB,GAAG,GAAG,EAAE,CAAA;IACd,EAAA,IAAMC,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACxB,gBAAgB,CAAC,CAAA;MAAC,IAAAyB,KAAA,GAAAA,SAAAA,KAAAA,GACnB;IAA5B,IAAA,IAAMC,GAAG,GAAAC,cAAA,CAAAC,EAAA,CAAA,CAAA;QACV,IAAMpB,MAAM,GAAGkB,GAAG,CAAA;IAClBL,IAAAA,GAAG,CAACb,MAAM,CAAC,GAAG,YAAa;IAAA,MAAA,KAAA,IAAAqB,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATtB,IAAI,GAAAuB,IAAAA,KAAA,CAAAH,IAAA,GAAAI,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA,EAAA,EAAA;IAAJxB,QAAAA,IAAI,CAAAwB,IAAA,CAAAH,GAAAA,SAAA,CAAAG,IAAA,CAAA,CAAA;IAAA,OAAA;IAClB1B,MAAAA,KAAK,CAACC,MAAM,EAAEC,IAAI,CAAC,CAAA;SACtB,CAAA;OACJ,CAAA;IALD,EAAA,KAAA,IAAAmB,EAAA,GAAA,CAAA,EAAAD,cAAA,GAAkBL,aAAa,EAAAM,EAAA,GAAAD,cAAA,CAAAI,MAAA,EAAAH,EAAA,EAAA,EAAA;QAAAH,KAAA,EAAA,CAAA;IAAA,GAAA;IAM/B,EAAA,OAAOJ,GAAG,CAAA;IACd,CAAC,EAAI;;IC/DT;IACA;AACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAaa,kBAAkB,gBAAA,YAAA;IAC3B,EAAA,SAAAA,qBAAc;IACV,IAAA,IAAI,CAACC,sBAAsB,GAAG,IAAIC,GAAG,EAAE,CAAA;IAC3C,GAAA;IACA;IACJ;IACA;IACA;IACA;IAJI,EAAA,IAAAC,MAAA,GAAAH,kBAAA,CAAAI,SAAA,CAAA;MAAAD,MAAA,CAKAE,gBAAgB,GAAhB,SAAAA,iBAAiBC,IAAI,EAAEC,QAAQ,EAAE;IAC7B,IAAA,IAAMC,GAAG,GAAG,IAAI,CAACC,wBAAwB,CAACH,IAAI,CAAC,CAAA;IAC/CE,IAAAA,GAAG,CAACE,GAAG,CAACH,QAAQ,CAAC,CAAA;IACrB,GAAA;IACA;IACJ;IACA;IACA;IACA,MAJI;MAAAJ,MAAA,CAKAQ,mBAAmB,GAAnB,SAAAA,oBAAoBL,IAAI,EAAEC,QAAQ,EAAE;QAChC,IAAI,CAACE,wBAAwB,CAACH,IAAI,CAAC,CAACM,MAAM,CAACL,QAAQ,CAAC,CAAA;IACxD,GAAA;IACA;IACJ;IACA;IACA,MAHI;IAAAJ,EAAAA,MAAA,CAIAU,aAAa,GAAb,SAAAA,aAAAA,CAAc5D,KAAK,EAAE;QACjBA,KAAK,CAAC6D,MAAM,GAAG,IAAI,CAAA;QACnB,IAAMC,SAAS,GAAG,IAAI,CAACN,wBAAwB,CAACxD,KAAK,CAACqD,IAAI,CAAC,CAAA;IAC3D,IAAA,KAAA,IAAAU,SAAA,GAAAC,+BAAA,CAAuBF,SAAS,CAAA,EAAAG,KAAA,EAAA,CAAA,CAAAA,KAAA,GAAAF,SAAA,EAAA,EAAAG,IAAA,GAAE;IAAA,MAAA,IAAvBZ,QAAQ,GAAAW,KAAA,CAAAE,KAAA,CAAA;UACfb,QAAQ,CAACtD,KAAK,CAAC,CAAA;IACnB,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA,MAPI;IAAAkD,EAAAA,MAAA,CAQAM,wBAAwB,GAAxB,SAAAA,wBAAAA,CAAyBH,IAAI,EAAE;QAC3B,IAAI,CAAC,IAAI,CAACL,sBAAsB,CAACoB,GAAG,CAACf,IAAI,CAAC,EAAE;UACxC,IAAI,CAACL,sBAAsB,CAACqB,GAAG,CAAChB,IAAI,EAAE,IAAIiB,GAAG,EAAE,CAAC,CAAA;IACpD,KAAA;IACA,IAAA,OAAO,IAAI,CAACtB,sBAAsB,CAACuB,GAAG,CAAClB,IAAI,CAAC,CAAA;OAC/C,CAAA;IAAA,EAAA,OAAAN,kBAAA,CAAA;IAAA,CAAA,EAAA;;IC1DL;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACO,SAASyB,SAASA,CAACC,IAAI,EAAEC,IAAI,EAAE;MAClC,IAAAC,SAAA,GAAiBC,QAAQ;QAAjBC,IAAI,GAAAF,SAAA,CAAJE,IAAI,CAAA;IACZ,EAAA,OAAO,IAAIC,GAAG,CAACL,IAAI,EAAEI,IAAI,CAAC,CAACA,IAAI,KAAK,IAAIC,GAAG,CAACJ,IAAI,EAAEG,IAAI,CAAC,CAACA,IAAI,CAAA;IAChE;;ICpBA;IACA;AACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;AACA,QAAaE,YAAY,GACrB,SAAAA,aAAY1B,IAAI,EAAE2B,KAAK,EAAE;MACrB,IAAI,CAAC3B,IAAI,GAAGA,IAAI,CAAA;IAChBjB,EAAAA,MAAM,CAAC6C,MAAM,CAAC,IAAI,EAAED,KAAK,CAAC,CAAA;IAC9B;;ICHJ;IACA;IACA;;IAmEO,SAASE,MAAMA,CAACf,KAAK,EAAE3D,IAAI,EAAE2E,MAAM,EAAE;IAC3C,EAAA,IAAIA,MAAM,EAAE;IACX,IAAA,OAAO3E,IAAI,GAAGA,IAAI,CAAC2D,KAAK,CAAC,GAAGA,KAAK,CAAA;IAClC,GAAA;IACA,EAAA,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAAC3D,IAAI,EAAE;IAC1B2D,IAAAA,KAAK,GAAGzE,OAAO,CAACC,OAAO,CAACwE,KAAK,CAAC,CAAA;IAC/B,GAAA;MACA,OAAO3D,IAAI,GAAG2D,KAAK,CAAC3D,IAAI,CAACA,IAAI,CAAC,GAAG2D,KAAK,CAAA;IACvC,CAAA;IA1EA,IAAMiB,wBAAwB,GAAG,GAAG,CAAA;IACpC;IACA;;IAkDO,SAASC,MAAMA,CAACC,CAAC,EAAE;IACzB,EAAA,OAAO,YAAW;IACjB,IAAA,KAAK,IAAIhE,IAAI,GAAG,EAAE,EAAEiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5C,SAAS,CAACC,MAAM,EAAE2C,CAAC,EAAE,EAAE;IACrDjE,MAAAA,IAAI,CAACiE,CAAC,CAAC,GAAG5C,SAAS,CAAC4C,CAAC,CAAC,CAAA;IACvB,KAAA;QACA,IAAI;IACH,MAAA,OAAO7F,OAAO,CAACC,OAAO,CAAC2F,CAAC,CAACzD,KAAK,CAAC,IAAI,EAAEP,IAAI,CAAC,CAAC,CAAA;SAC3C,CAAC,OAAMhC,CAAC,EAAE;IACV,MAAA,OAAOI,OAAO,CAACY,MAAM,CAAChB,CAAC,CAAC,CAAA;IACzB,KAAA;OACA,CAAA;IACF,CAAA;IA5DA,IAAMkG,6BAA6B,GAAG,KAAK,CAAA;IAC3C;IACA;;IAykBO,SAASC,MAAMA,GAAG,EACzB;IAzkBA,IAAMC,oBAAoB,GAAG;IAAErC,EAAAA,IAAI,EAAE,cAAA;IAAe,CAAC,CAAA;IACrD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IA2DO,SAASsC,aAAaA,CAACxB,KAAK,EAAEgB,MAAM,EAAE;MAC5C,IAAI,CAACA,MAAM,EAAE;IACZ,IAAA,OAAOhB,KAAK,IAAIA,KAAK,CAAC3D,IAAI,GAAG2D,KAAK,CAAC3D,IAAI,CAACiF,MAAM,CAAC,GAAG/F,OAAO,CAACC,OAAO,EAAE,CAAA;IACpE,GAAA;IACD,CAAA;AA9DMiG,QAAAA,OAAO,0BAAAC,mBAAA,EAAA;IACT;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI;IACA,EAAA,SAAAD,OAAYE,CAAAA,SAAS,EAAEC,eAAe,EAAO;IAAA,IAAA,IAAA3F,KAAA,CAAA;IAAA,IAAA,IAAtB2F,eAAe,KAAA,KAAA,CAAA,EAAA;UAAfA,eAAe,GAAG,EAAE,CAAA;IAAA,KAAA;IACvC3F,IAAAA,KAAA,GAAAyF,mBAAA,CAAAG,IAAA,KAAM,CAAC,IAAA,IAAA,CAAA;IACP5F,IAAAA,KAAA,CAAK6F,gBAAgB,GAAG,EAAE,CAAA;QAC1B7F,KAAA,CAAK8F,iBAAiB,GAAG,CAAC,CAAA;IAC1B;IACA9F,IAAAA,KAAA,CAAK+F,WAAW,GAAG,IAAIhG,QAAQ,EAAE,CAAA;IACjCC,IAAAA,KAAA,CAAKgG,eAAe,GAAG,IAAIjG,QAAQ,EAAE,CAAA;IACrCC,IAAAA,KAAA,CAAKiG,oBAAoB,GAAG,IAAIlG,QAAQ,EAAE,CAAA;QAC1CC,KAAA,CAAKkG,iBAAiB,GAAG,CAAC,CAAA;IAC1BlG,IAAAA,KAAA,CAAKmG,OAAO,GAAG,IAAIjC,GAAG,EAAE,CAAA;IACxB;IACR;IACA;QACQlE,KAAA,CAAKoG,cAAc,GAAG,YAAM;IACxB;IACA,MAAA,IAAMC,YAAY,GAAGrG,KAAA,CAAKsG,aAAa,CAAA;IACvC,MAAA,IAAMC,YAAY,GAAGF,YAAY,CAACG,UAAU,CAAA;IAC5C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAA,IAAMC,+BAA+B;IACrC;IACA;IACA;IACA;UACAzG,KAAA,CAAK8F,iBAAiB,GAAG,CAAC;IACtB;IACA;IACA;IACA,MAAA,CAAC1B,SAAS,CAACmC,YAAY,CAACb,SAAS,EAAE1F,KAAA,CAAK0G,UAAU,CAACC,QAAQ,EAAE,CAAC;IAC9D;IACA;IACA;UACAC,WAAW,CAACC,GAAG,EAAE,GAAG7G,KAAA,CAAKkG,iBAAiB,GAAGd,6BAA6B;IACxE;IACE;IACA,MAAA,IAAI,GACN,KAAK,CAAA;IACX,MAAA,IAAIqB,+BAA+B,EAAE;YACjCzG,KAAA,CAAK8G,WAAW,GAAGP,YAAY,CAAA;YAC/BF,YAAY,CAAC/C,mBAAmB,CAAC,aAAa,EAAEtD,KAAA,CAAKoG,cAAc,CAAC,CAAA;IACxE,OAAC,MACI;IACD;IACA;YACApG,KAAA,CAAK+G,GAAG,GAAGR,YAAY,CAAA;IACvBvG,QAAAA,KAAA,CAAKmG,OAAO,CAAC9C,GAAG,CAACkD,YAAY,CAAC,CAAA;IAC9BvG,QAAAA,KAAA,CAAK+F,WAAW,CAACxG,OAAO,CAACgH,YAAY,CAAC,CAAA;IACtC;IACA;IACA,QAA2C;IACvC,UAAA,IAAIlF,SAAS,CAAC2F,aAAa,CAACC,UAAU,EAAE;IACpC5G,YAAAA,MAAM,CAACM,GAAG,CAAC,iDAAiD,CAAC,CAAA;IACjE,WAAC,MACI;IACDN,YAAAA,MAAM,CAACM,GAAG,CAAC,iCAAiC,CAAC,CAAA;IACjD,WAAA;IACJ,SAAA;IACJ,OAAA;IACA;IACA;UACA,EAAEX,KAAA,CAAK8F,iBAAiB,CAAA;IACxB;IACA;UACAS,YAAY,CAACvD,gBAAgB,CAAC,aAAa,EAAEhD,KAAA,CAAKkH,cAAc,CAAC,CAAA;SACpE,CAAA;IACD;IACR;IACA;IACA;IACQlH,IAAAA,KAAA,CAAKkH,cAAc,GAAG,UAACC,aAAa,EAAK;IACrC;IACA,MAAA,IAAMd,YAAY,GAAGrG,KAAA,CAAKsG,aAAa,CAAA;IACvC,MAAA,IAAMlH,EAAE,GAAG+H,aAAa,CAAC1D,MAAM,CAAA;IAC/B,MAAA,IAAQ2D,KAAK,GAAKhI,EAAE,CAAZgI,KAAK,CAAA;IACb,MAAA,IAAMC,UAAU,GAAGjI,EAAE,KAAKY,KAAA,CAAK8G,WAAW,CAAA;IAC1C,MAAA,IAAMQ,UAAU,GAAG;IACflI,QAAAA,EAAE,EAAFA,EAAE;IACFiI,QAAAA,UAAU,EAAVA,UAAU;IACVF,QAAAA,aAAa,EAAbA,aAAAA;WACH,CAAA;IACD,MAAA,IAAI,CAACE,UAAU,IAAIrH,KAAA,CAAKuH,SAAS,EAAE;YAC/BD,UAAU,CAACE,QAAQ,GAAG,IAAI,CAAA;IAC9B,OAAA;UACAxH,KAAA,CAAKwD,aAAa,CAAC,IAAImB,YAAY,CAACyC,KAAK,EAAEE,UAAU,CAAC,CAAC,CAAA;UACvD,IAAIF,KAAK,KAAK,WAAW,EAAE;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACApH,QAAAA,KAAA,CAAKyH,eAAe,GAAGzI,IAAI,CAAC0I,UAAU,CAAC,YAAM;IACzC;cACA,IAAIN,KAAK,KAAK,WAAW,IAAIf,YAAY,CAACsB,OAAO,KAAKvI,EAAE,EAAE;gBACtDY,KAAA,CAAKwD,aAAa,CAAC,IAAImB,YAAY,CAAC,SAAS,EAAE2C,UAAU,CAAC,CAAC,CAAA;IAC3D,YAA2C;IACvC,cAAA,IAAID,UAAU,EAAE;IACZhH,gBAAAA,MAAM,CAACO,IAAI,CAAC,kDAAkD,GAC1D,uDAAuD,CAAC,CAAA;IAChE,eAAC,MACI;IACDP,gBAAAA,MAAM,CAACO,IAAI,CAAC,kDAAkD,GAC1D,oDAAoD,CAAC,CAAA;IAC7D,eAAA;IACJ,aAAA;IACJ,WAAA;aACH,EAAEoE,wBAAwB,CAAC,CAAA;IAChC,OAAC,MACI,IAAIoC,KAAK,KAAK,YAAY,EAAE;IAC7BQ,QAAAA,YAAY,CAAC5H,KAAA,CAAKyH,eAAe,CAAC,CAAA;YAClC,IAAI,CAACJ,UAAU,EAAE;IACbrH,UAAAA,KAAA,CAAKgG,eAAe,CAACzG,OAAO,CAACH,EAAE,CAAC,CAAA;IACpC,SAAA;IACJ,OAAA;IACA,MAA2C;IACvC,QAAA,QAAQgI,KAAK;IACT,UAAA,KAAK,WAAW;IACZ,YAAA,IAAIC,UAAU,EAAE;IACZhH,cAAAA,MAAM,CAACO,IAAI,CAAC,4CAA4C,GACpD,iDAAiD,CAAC,CAAA;IAC1D,aAAC,MACI;IACDP,cAAAA,MAAM,CAACM,GAAG,CAAC,sCAAsC,CAAC,CAAA;IACtD,aAAA;IACA,YAAA,MAAA;IACJ,UAAA,KAAK,WAAW;IACZ,YAAA,IAAI0G,UAAU,EAAE;IACZhH,cAAAA,MAAM,CAACO,IAAI,CAAC,2CAA2C,CAAC,CAAA;IAC5D,aAAC,MACI;IACDP,cAAAA,MAAM,CAACM,GAAG,CAAC,sCAAsC,CAAC,CAAA;IAClD,cAAA,IAAIvB,EAAE,KAAKiC,SAAS,CAAC2F,aAAa,CAACC,UAAU,EAAE;oBAC3C5G,MAAM,CAACO,IAAI,CAAC,8CAA8C,GACtD,8CAA8C,GAC9C,0CAA0C,CAAC,CAAA;IACnD,eAAA;IACJ,aAAA;IACA,YAAA,MAAA;IACJ,UAAA,KAAK,WAAW;IACZ,YAAA,IAAIxB,EAAE,KAAKY,KAAA,CAAK6H,wBAAwB,EAAE;IACtCxH,cAAAA,MAAM,CAACM,GAAG,CAAC,sDAAsD,CAAC,CAAA;IACtE,aAAC,MACI,IAAI,CAAC0G,UAAU,EAAE;IAClBhH,cAAAA,MAAM,CAACM,GAAG,CAAC,0CAA0C,CAAC,CAAA;IAC1D,aAAA;IACA,YAAA,MAAA;IACR,SAAA;IACJ,OAAA;SACH,CAAA;IACD;IACR;IACA;IACA;IACQX,IAAAA,KAAA,CAAK8H,mBAAmB,GAAG,UAACX,aAAa,EAAK;IAC1C,MAAA,IAAM/H,EAAE,GAAGY,KAAA,CAAK+G,GAAG,CAAA;UACnB,IAAMM,UAAU,GAAGjI,EAAE,KAAKiC,SAAS,CAAC2F,aAAa,CAACC,UAAU,CAAA;IAC5D;IACA;IACA;IACA;IACAjH,MAAAA,KAAA,CAAKwD,aAAa,CAAC,IAAImB,YAAY,CAAC,aAAa,EAAE;IAC/C0C,QAAAA,UAAU,EAAVA,UAAU;IACVF,QAAAA,aAAa,EAAbA,aAAa;IACb/H,QAAAA,EAAE,EAAFA,EAAE;YACFoI,QAAQ,EAAExH,KAAA,CAAKuH,SAAAA;IACnB,OAAC,CAAC,CAAC,CAAA;UACH,IAAI,CAACF,UAAU,EAAE;IACb,QAA2C;IACvChH,UAAAA,MAAM,CAACM,GAAG,CAAC,sDAAsD,CAAC,CAAA;IACtE,SAAA;IACAX,QAAAA,KAAA,CAAKiG,oBAAoB,CAAC1G,OAAO,CAACH,EAAE,CAAC,CAAA;IACzC,OAAA;SACH,CAAA;IACD;IACR;IACA;IACA;IACQY,IAAAA,KAAA,CAAK+H,UAAU,GAAA9C,MAAA,CAAA,UAAUkC,aAAa,EAAK;IACvC;IACA;IACA,MAAA,IAAQ9H,IAAI,GAAoB8H,aAAa,CAArC9H,IAAI;YAAE2I,KAAK,GAAab,aAAa,CAA/Ba,KAAK;YAAEC,MAAM,GAAKd,aAAa,CAAxBc,MAAM,CAAA;IAC3B;IACA;IAAA,MAAA,OAAAnD,MAAA,CACM9E,KAAA,CAAKkI,KAAK,EAAE,EAAA,YAAA;IAAA,QAAA,IAOdlI,KAAA,CAAKmG,OAAO,CAACnC,GAAG,CAACiE,MAAM,CAAC,EAAA;IACxBjI,UAAAA,KAAA,CAAKwD,aAAa,CAAC,IAAImB,YAAY,CAAC,SAAS,EAAE;IAC3C;IACA;IACAtF,YAAAA,IAAI,EAAJA,IAAI;IACJ8H,YAAAA,aAAa,EAAbA,aAAa;IACba,YAAAA,KAAK,EAALA,KAAK;IACL5I,YAAAA,EAAE,EAAE6I,MAAAA;IACR,WAAC,CAAC,CAAC,CAAA;IAAC,SAAA;WAdR,CAAA,CAAA;IACA;IACA;IACA;IACA;IACA;SAWH,CAAA,CAAA;QACDjI,KAAA,CAAK0G,UAAU,GAAGhB,SAAS,CAAA;QAC3B1F,KAAA,CAAK6F,gBAAgB,GAAGF,eAAe,CAAA;IACvC;IACA;IACA;QACAtE,SAAS,CAAC2F,aAAa,CAAChE,gBAAgB,CAAC,SAAS,EAAEhD,KAAA,CAAK+H,UAAU,CAAC,CAAA;IAAC,IAAA,OAAA/H,KAAA,CAAA;IACzE,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MATImI,cAAA,CAAA3C,OAAA,EAAAC,mBAAA,CAAA,CAAA;IAAA,EAAA,IAAA3C,MAAA,GAAA0C,OAAA,CAAAzC,SAAA,CAAA;IAAAD,EAAAA,MAAA,CAUMsF,QAAQ,GAAAA,SAAAA,QAAAA,CAAAC,KAAA,EAAA;IAAA,IAAA,IAAAC,IAAA,GAAAD,KAAA,cAAyB,EAAE,GAAAA,KAAA;UAAAE,cAAA,GAAAD,IAAA,CAAxBE,SAAS;IAATA,MAAAA,SAAS,GAAAD,cAAA,KAAG,KAAA,CAAA,GAAA,KAAK,GAAAA,cAAA,CAAA;QAAA,IAAS;UAAA,IAAAE,MAAA,GAE/B,IAAI,CAAA;IADZ,MAAA,IAAIC,KAAoB,KAAK,YAAY,EAAE;YACvC,IAAID,MAAA,CAAKvC,iBAAiB,EAAE;IACxB7F,UAAAA,MAAM,CAACQ,KAAK,CAAC,qDAAqD,GAC9D,iDAAiD,CAAC,CAAA;IACtD,UAAA,OAAAiE,MAAA,EAAA,CAAA;IACJ,SAAA;IACJ,OAAA;UAAC,OAAAA,MAAA,CAAA6D,OAAA,CAAA,YAAA;IAAA,QAAA,IACG,CAACH,SAAS,IAAII,QAAQ,CAACC,UAAU,KAAK,UAAU,EAAA;IAAA,UAAA,OAAAtD,aAAA,CAC1C,IAAIjG,OAAO,CAAC,UAACwJ,GAAG,EAAA;IAAA,YAAA,OAAKC,MAAM,CAAC/F,gBAAgB,CAAC,MAAM,EAAE8F,GAAG,CAAC,CAAA;eAAC,CAAA,CAAA,CAAA;IAAA,SAAA;IAAA,OAAA,EAAA,YAAA;IAEpE;IACA;YACAL,MAAA,CAAKlB,SAAS,GAAGyB,OAAO,CAAC3H,SAAS,CAAC2F,aAAa,CAACC,UAAU,CAAC,CAAA;IAC5D;IACA;IACA;IACAwB,QAAAA,MAAA,CAAKZ,wBAAwB,GAAGY,MAAA,CAAKQ,6BAA6B,EAAE,CAAA;YAAC,OAAAnE,MAAA,CAC1C2D,MAAA,CAAKS,eAAe,EAAE,YAAAC,qBAAA,EAAA;cAAjDV,MAAA,CAAKnC,aAAa,GAAA6C,qBAA+B,CAAA;IACjD;IACA;cACA,IAAIV,MAAA,CAAKZ,wBAAwB,EAAE;IAC/BY,YAAAA,MAAA,CAAK1B,GAAG,GAAG0B,MAAA,CAAKZ,wBAAwB,CAAA;gBACxCY,MAAA,CAAKzC,eAAe,CAACzG,OAAO,CAACkJ,MAAA,CAAKZ,wBAAwB,CAAC,CAAA;gBAC3DY,MAAA,CAAKxC,oBAAoB,CAAC1G,OAAO,CAACkJ,MAAA,CAAKZ,wBAAwB,CAAC,CAAA;gBAChEY,MAAA,CAAKZ,wBAAwB,CAAC7E,gBAAgB,CAAC,aAAa,EAAEyF,MAAA,CAAKvB,cAAc,EAAE;IAAEkC,cAAAA,IAAI,EAAE,IAAA;IAAK,aAAC,CAAC,CAAA;IACtG,WAAA;IACA;IACA;IACA;IACA;IACA;IACA,UAAA,IAAMC,SAAS,GAAGZ,MAAA,CAAKnC,aAAa,CAACqB,OAAO,CAAA;IAC5C,UAAA,IAAI0B,SAAS,IACTjF,SAAS,CAACiF,SAAS,CAAC3D,SAAS,EAAE+C,MAAA,CAAK/B,UAAU,CAACC,QAAQ,EAAE,CAAC,EAAE;IAC5D;IACA;gBACA8B,MAAA,CAAK1B,GAAG,GAAGsC,SAAS,CAAA;IACpB;IACA;gBACAlJ,WAAW,CAACb,OAAO,CAACC,OAAO,EAAE,CAACa,IAAI,CAAC,YAAM;IACrCqI,cAAAA,MAAA,CAAKjF,aAAa,CAAC,IAAImB,YAAY,CAAC,SAAS,EAAE;IAC3CvF,gBAAAA,EAAE,EAAEiK,SAAS;IACbC,gBAAAA,wBAAwB,EAAE,IAAA;IAC9B,eAAC,CAAC,CAAC,CAAA;IACH,cAAA,IAAIZ,KAAoB,KAAK,YAAY,EAAE;IACvCrI,gBAAAA,MAAM,CAACO,IAAI,CAAC,mDAAmD,GAC3D,sCAAsC,CAAC,CAAA;IAC/C,eAAA;IACJ,aAAC,CAAC,CAAC,CAAA;IACP,WAAA;IACA;cACA,IAAI6H,MAAA,CAAK1B,GAAG,EAAE;gBACV0B,MAAA,CAAK1C,WAAW,CAACxG,OAAO,CAACkJ,MAAA,CAAK1B,GAAG,CAAC,CAAA;gBAClC0B,MAAA,CAAKtC,OAAO,CAAC9C,GAAG,CAACoF,MAAA,CAAK1B,GAAG,CAAC,CAAA;IAC9B,WAAA;IACA,UAAA,IAAI2B,KAAoB,KAAK,YAAY,EAAE;IACvCrI,YAAAA,MAAM,CAACM,GAAG,CAAC,yCAAyC,EAAE8H,MAAA,CAAK/B,UAAU,CAACC,QAAQ,EAAE,CAAC,CAAA;IACjF,YAAA,IAAItF,SAAS,CAAC2F,aAAa,CAACC,UAAU,EAAE;kBACpC,IAAIwB,MAAA,CAAKZ,wBAAwB,EAAE;IAC/BxH,gBAAAA,MAAM,CAACK,KAAK,CAAC,4CAA4C,GACrD,mCAAmC,CAAC,CAAA;IAC5C,eAAC,MACI;oBACDL,MAAM,CAACK,KAAK,CAAC,kDAAkD,GAC3D,8DAA8D,GAC9D,uBAAuB,CAAC,CAAA;IAChC,eAAA;IACJ,aAAA;IACA,YAAA,IAAM6I,uBAAuB,GAAG,SAA1BA,uBAAuBA,GAAS;kBAClC,IAAMC,QAAQ,GAAG,IAAI9E,GAAG,CAAC+D,MAAA,CAAK5C,gBAAgB,CAAC4D,KAAK,IAAIhB,MAAA,CAAK/B,UAAU,CAACC,QAAQ,EAAE,EAAEiC,QAAQ,CAACc,OAAO,CAAC,CAAA;IACrG,cAAA,IAAMC,gBAAgB,GAAG,IAAIjF,GAAG,CAAC,IAAI,EAAE8E,QAAQ,CAAC/E,IAAI,CAAC,CAACmF,QAAQ,CAAA;kBAC9D,OAAO,CAACpF,QAAQ,CAACoF,QAAQ,CAACC,UAAU,CAACF,gBAAgB,CAAC,CAAA;iBACzD,CAAA;gBACD,IAAIJ,uBAAuB,EAAE,EAAE;IAC3BlJ,cAAAA,MAAM,CAACO,IAAI,CAAC,sDAAsD,GAC9D,qCAAqC,CAAC,CAAA;IAC9C,aAAA;IACJ,WAAA;cACA6H,MAAA,CAAKnC,aAAa,CAACtD,gBAAgB,CAAC,aAAa,EAAEyF,MAAA,CAAKrC,cAAc,CAAC,CAAA;cACvE/E,SAAS,CAAC2F,aAAa,CAAChE,gBAAgB,CAAC,kBAAkB,EAAEyF,MAAA,CAAKX,mBAAmB,CAAC,CAAA;cACtF,OAAOW,MAAA,CAAKnC,aAAa,CAAA;IAAC,SAAA,CAAA,CAAA;IAAA,OAAA,CAAA,CAAA,CAAA;IAC9B,KAAC,QAAApH,CAAA,EAAA;IAAA,MAAA,OAAAI,OAAA,CAAAY,MAAA,CAAAhB,CAAA,CAAA,CAAA;IAAA,KAAA;IAAA,GAAA;IACD;IACJ;IACA;IAFI,GAAA;MAAA4D,MAAA,CAGMgH,MAAM,GAAA,SAAAA,MAAA,GAAA;QAAA,IAAG;UAAA,IAAAC,MAAA,GACN,IAAI,CAAA;IAAT,MAAA,IAAI,CAACA,MAAA,CAAKzD,aAAa,EAAE;IACrB,QAAA,IAAIoC,KAAoB,KAAK,YAAY,EAAE;IACvCrI,UAAAA,MAAM,CAACQ,KAAK,CAAC,2CAA2C,GACpD,wDAAwD,CAAC,CAAA;IACjE,SAAA;IACA,QAAA,OAAAiE,MAAA,EAAA,CAAA;IACJ,OAAA;IACA;UAAA,OAAAA,MAAA,CAAAS,aAAA,CACMwE,MAAA,CAAKzD,aAAa,CAACwD,MAAM,EAAE,CAAA,CAAA,CAAA;IACrC,KAAC,QAAA5K,CAAA,EAAA;IAAA,MAAA,OAAAI,OAAA,CAAAY,MAAA,CAAAhB,CAAA,CAAA,CAAA;IAAA,KAAA;IAAA,GAAA;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IARI,GAAA;IA2BA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAdI4D,EAAAA,MAAA,CAeAoF,KAAK,GAAL,SAAAA,QAAQ;IACJ;IACA;IACA,IAAA,OAAO,IAAI,CAACnB,GAAG,KAAKiD,SAAS,GACvB1K,OAAO,CAACC,OAAO,CAAC,IAAI,CAACwH,GAAG,CAAC,GACzB,IAAI,CAAChB,WAAW,CAAC9F,OAAO,CAAA;IAClC,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACI;IACA;IAAA,GAAA;IAAA6C,EAAAA,MAAA,CACM3D,SAAS,GAAAA,SAAAA,WAAAA,CAACE,IAAI,EAAA;QAAA,IAAE;UAAA,IAAA4K,MAAA,GACD,IAAI,CAAA;UAAA,OAAAnF,MAAA,CAAJmF,MAAA,CAAK/B,KAAK,EAAE,YAAvB9I,EAAE,EAAA;IACR,QAAA,OAAOD,SAAS,CAACC,EAAE,EAAEC,IAAI,CAAC,CAAA;IAAC,OAAA,CAAA,CAAA;IAC/B,KAAC,QAAAH,CAAA,EAAA;IAAA,MAAA,OAAAI,OAAA,CAAAY,MAAA,CAAAhB,CAAA,CAAA,CAAA;IAAA,KAAA;IAAA,GAAA;IACD;IACJ;IACA;IACA;IACA;IACA;IACA;IANI,GAAA;IAAA4D,EAAAA,MAAA,CAOAoH,kBAAkB,GAAlB,SAAAA,qBAAqB;QACjB,IAAI,IAAI,CAAC5D,aAAa,IAAI,IAAI,CAACA,aAAa,CAACqB,OAAO,EAAE;UAClD,KAAKxI,SAAS,CAAC,IAAI,CAACmH,aAAa,CAACqB,OAAO,EAAErC,oBAAoB,CAAC,CAAA;IACpE,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA,MANI;IAAAxC,EAAAA,MAAA,CAOAmG,6BAA6B,GAA7B,SAAAA,gCAAgC;IAC5B,IAAA,IAAMhC,UAAU,GAAG5F,SAAS,CAAC2F,aAAa,CAACC,UAAU,CAAA;IACrD,IAAA,IAAIA,UAAU,IACV7C,SAAS,CAAC6C,UAAU,CAACvB,SAAS,EAAE,IAAI,CAACgB,UAAU,CAACC,QAAQ,EAAE,CAAC,EAAE;IAC7D,MAAA,OAAOM,UAAU,CAAA;IACrB,KAAC,MACI;IACD,MAAA,OAAO+C,SAAS,CAAA;IACpB,KAAA;IACJ,GAAA;IACA;IACJ;IACA;IACA;IACA;IACA,MALI;MAAAlH,MAAA,CAMMoG,eAAe,GAAA,SAAAA,eAAA,GAAA;QAAA,IAAG;UAAA,IAAAiB,MAAA,GAKmC,IAAI,CAAA;UAAA,OAAArF,MAAA,CAAAsF,MAAA,CAJvD,YAAA;IACA;IACA;IACA;IAAA,QAAA,OAAAtF,MAAA,CACkBzD,SAAS,CAAC2F,aAAa,CAACoB,QAAQ,CAAC+B,MAAA,CAAKzD,UAAU,EAAEyD,MAAA,CAAKtE,gBAAgB,CAAC,YAApFwE,GAAG,EAAA;IACT;IACA;IACA;IACAF,UAAAA,MAAA,CAAKjE,iBAAiB,GAAGU,WAAW,CAACC,GAAG,EAAE,CAAA;IAC1C,UAAA,OAAOwD,GAAG,CAAA;IAAC,SAAA,CAAA,CAAA;WACd,EAAA,UACMxJ,KAAK,EAAE;IACV,QAAA,IAAI6H,KAAoB,KAAK,YAAY,EAAE;IACvCrI,UAAAA,MAAM,CAACQ,KAAK,CAACA,KAAK,CAAC,CAAA;IACvB,SAAA;IACA;IACA,QAAA,MAAMA,KAAK,CAAA;WACd,CAAA,CAAA,CAAA;IACL,KAAC,QAAA3B,CAAA,EAAA;IAAA,MAAA,OAAAI,OAAA,CAAAY,MAAA,CAAAhB,CAAA,CAAA,CAAA;IAAA,KAAA;IAAA,GAAA,CAAA;MAAA,OAAAoL,YAAA,CAAA9E,OAAA,EAAA,CAAA;QAAArD,GAAA,EAAA,QAAA;QAAAgC,GAAA,EAjHD,SAAAA,GAAAA,GAAa;IACT,MAAA,OAAO,IAAI,CAAC6B,eAAe,CAAC/F,OAAO,CAAA;IACvC,KAAA;IACA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAXI,GAAA,EAAA;QAAAkC,GAAA,EAAA,aAAA;QAAAgC,GAAA,EAYA,SAAAA,GAAAA,GAAkB;IACd,MAAA,OAAO,IAAI,CAAC8B,oBAAoB,CAAChG,OAAO,CAAA;IAC5C,KAAA;IAAC,GAAA,CAAA,CAAA,CAAA;IAAA,CAAA,CA9WiB0C,kBAAkB,EAAA;IA4fjC,SAAAgG,OAAiB4B,CAAAA,IAAI,EAAEnK,IAAI,EAAE;IACnC,EAAA,IAAIoK,MAAM,GAAGD,IAAI,EAAE,CAAA;IACnB,EAAA,IAAIC,MAAM,IAAIA,MAAM,CAACpK,IAAI,EAAE;IAC1B,IAAA,OAAOoK,MAAM,CAACpK,IAAI,CAACA,IAAI,CAAC,CAAA;IACzB,GAAA;MACA,OAAOA,IAAI,CAACoK,MAAM,CAAC,CAAA;IACpB,CAAC;IAhDD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAzDO,SAAAJ,MAAgBG,CAAAA,IAAI,EAAEE,OAAO,EAAE;MACrC,IAAI;IACH,IAAA,IAAID,MAAM,GAAGD,IAAI,EAAE,CAAA;OACnB,CAAC,OAAMrL,CAAC,EAAE;QACV,OAAOuL,OAAO,CAACvL,CAAC,CAAC,CAAA;IAClB,GAAA;IACA,EAAA,IAAIsL,MAAM,IAAIA,MAAM,CAACpK,IAAI,EAAE;QAC1B,OAAOoK,MAAM,CAACpK,IAAI,CAAC,KAAK,CAAC,EAAEqK,OAAO,CAAC,CAAA;IACpC,GAAA;IACA,EAAA,OAAOD,MAAM,CAAA;IACd;;;;;;;;;;;;"}