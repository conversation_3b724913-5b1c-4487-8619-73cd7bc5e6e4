{"name": "workbox-streams", "version": "7.3.0", "license": "MIT", "author": "Google's Web DevRel Team and Google's Aurora Team", "description": "A library that makes it easier to work with Streams in the browser.", "repository": {"type": "git", "url": "git+https://github.com/googlechrome/workbox.git"}, "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw", "streams", "readablestream"], "workbox": {"browserNamespace": "workbox.streams", "packageType": "sw"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "dependencies": {"workbox-core": "7.3.0", "workbox-routing": "7.3.0"}, "gitHead": "c77dceb54d4af1749db95316710d6430e82b0c48"}