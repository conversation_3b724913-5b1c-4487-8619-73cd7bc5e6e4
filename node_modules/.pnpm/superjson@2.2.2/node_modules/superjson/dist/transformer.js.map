{"version": 3, "file": "transformer.js", "sourceRoot": "", "sources": ["../src/transformer.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,QAAQ,EACR,MAAM,EACN,UAAU,EACV,KAAK,EACL,UAAU,EACV,QAAQ,EACR,KAAK,EACL,WAAW,EACX,QAAQ,EACR,OAAO,EACP,OAAO,EACP,YAAY,EAEZ,KAAK,GACN,MAAM,SAAS,CAAC;AACjB,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AA2BpC,SAAS,oBAAoB,CAC3B,YAAsD,EACtD,UAAa,EACb,SAA4C,EAC5C,WAA8C;IAE9C,OAAO;QACL,YAAY;QACZ,UAAU;QACV,SAAS;QACT,WAAW;KACZ,CAAC;AACJ,CAAC;AAED,MAAM,WAAW,GAAG;IAClB,oBAAoB,CAClB,WAAW,EACX,WAAW,EACX,GAAG,EAAE,CAAC,IAAI,EACV,GAAG,EAAE,CAAC,SAAS,CAChB;IACD,oBAAoB,CAClB,QAAQ,EACR,QAAQ,EACR,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,EACjB,CAAC,CAAC,EAAE;QACF,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;YACjC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;SAClB;QAED,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAE/C,OAAO,CAAQ,CAAC;IAClB,CAAC,CACF;IACD,oBAAoB,CAClB,MAAM,EACN,MAAM,EACN,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,EACpB,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CACjB;IAED,oBAAoB,CAClB,OAAO,EACP,OAAO,EACP,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE;QACf,MAAM,SAAS,GAAQ;YACrB,IAAI,EAAE,CAAC,CAAC,IAAI;YACZ,OAAO,EAAE,CAAC,CAAC,OAAO;SACnB,CAAC;QAEF,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACzC,SAAS,CAAC,IAAI,CAAC,GAAI,CAAS,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC,EACD,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE;QACf,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;QAChB,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QAElB,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACxC,CAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,CAAC;IACX,CAAC,CACF;IAED,oBAAoB,CAClB,QAAQ,EACR,QAAQ,EACR,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EACX,KAAK,CAAC,EAAE;QACN,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QACpD,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACtD,OAAO,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC,CACF;IAED,oBAAoB,CAClB,KAAK,EACL,KAAK;IACL,4BAA4B;IAC5B,8CAA8C;IAC9C,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EACpB,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAChB;IACD,oBAAoB,CAClB,KAAK,EACL,KAAK,EACL,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,EACrB,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAChB;IAED,oBAAoB,CAClB,CAAC,CAAC,EAAe,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAClD,QAAQ,EACR,CAAC,CAAC,EAAE;QACF,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;YACjB,OAAO,KAAK,CAAC;SACd;QAED,IAAI,CAAC,GAAG,CAAC,EAAE;YACT,OAAO,UAAU,CAAC;SACnB;aAAM;YACL,OAAO,WAAW,CAAC;SACpB;IACH,CAAC,EACD,MAAM,CACP;IAED,oBAAoB,CAClB,CAAC,CAAC,EAAe,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAClD,QAAQ,EACR,GAAG,EAAE;QACH,OAAO,IAAI,CAAC;IACd,CAAC,EACD,MAAM,CACP;IAED,oBAAoB,CAClB,KAAK,EACL,KAAK,EACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,EACjB,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAChB;CACF,CAAC;AAEF,SAAS,uBAAuB,CAC9B,YAAsD,EACtD,UAA6C,EAC7C,SAA4C,EAC5C,WAAoD;IAEpD,OAAO;QACL,YAAY;QACZ,UAAU;QACV,SAAS;QACT,WAAW;KACZ,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,GAAG,uBAAuB,CACxC,CAAC,CAAC,EAAE,SAAS,EAAe,EAAE;IAC5B,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;QACf,MAAM,YAAY,GAAG,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACjE,OAAO,YAAY,CAAC;KACrB;IACD,OAAO,KAAK,CAAC;AACf,CAAC,EACD,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE;IACf,MAAM,UAAU,GAAG,SAAS,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAC7D,OAAO,CAAC,QAAQ,EAAE,UAAW,CAAC,CAAC;AACjC,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAClB,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE;IAClB,MAAM,KAAK,GAAG,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;KACzD;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CACF,CAAC;AAEF,MAAM,iBAAiB,GAAG;IACxB,SAAS;IACT,UAAU;IACV,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,iBAAiB;CAClB,CAAC,MAAM,CAAwC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;IAC5D,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IACtB,OAAO,GAAG,CAAC;AACb,CAAC,EAAE,EAAE,CAAC,CAAC;AAEP,MAAM,cAAc,GAAG,uBAAuB,CAC5C,YAAY,EACZ,CAAC,CAAC,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EACxC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EACX,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IACP,MAAM,IAAI,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAErC,IAAI,CAAC,IAAI,EAAE;QACT,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;KAC9D;IAED,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC,CACF,CAAC;AAEF,MAAM,UAAU,2BAA2B,CACzC,cAAmB,EACnB,SAAoB;IAEpB,IAAI,cAAc,EAAE,WAAW,EAAE;QAC/B,MAAM,YAAY,GAAG,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,aAAa,CAC1D,cAAc,CAAC,WAAW,CAC3B,CAAC;QACF,OAAO,YAAY,CAAC;KACrB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,SAAS,GAAG,uBAAuB,CACvC,2BAA2B,EAC3B,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;IACnB,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IAC5E,OAAO,CAAC,OAAO,EAAE,UAAW,CAAC,CAAC;AAChC,CAAC,EACD,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;IACnB,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,CAAC,eAAe,CAC1D,KAAK,CAAC,WAAW,CAClB,CAAC;IACF,IAAI,CAAC,YAAY,EAAE;QACjB,OAAO,EAAE,GAAG,KAAK,EAAE,CAAC;KACrB;IAED,MAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QAC1B,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE;IAClB,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,IAAI,KAAK,CACb,wCAAwC,CAAC,CAAC,CAAC,CAAC,mFAAmF,CAChI,CAAC;KACH;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1D,CAAC,CACF,CAAC;AAEF,MAAM,UAAU,GAAG,uBAAuB,CACxC,CAAC,KAAK,EAAE,SAAS,EAAgB,EAAE;IACjC,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACrE,CAAC,EACD,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;IACnB,MAAM,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC,cAAc,CACpE,KAAK,CACL,CAAC;IACH,OAAO,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AACtC,CAAC,EACD,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;IACnB,MAAM,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC,cAAc,CACpE,KAAK,CACL,CAAC;IACH,OAAO,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACtC,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,EAAE;IAClB,MAAM,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;KAC/D;IACD,OAAO,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CACF,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;AAE3E,MAAM,CAAC,MAAM,cAAc,GAAG,CAC5B,KAAU,EACV,SAAoB,EAC8B,EAAE;IACpD,MAAM,uBAAuB,GAAG,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,EAAE,CAC7D,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CACpC,CAAC;IACF,IAAI,uBAAuB,EAAE;QAC3B,OAAO;YACL,KAAK,EAAE,uBAAuB,CAAC,SAAS,CAAC,KAAc,EAAE,SAAS,CAAC;YACnE,IAAI,EAAE,uBAAuB,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC;SAC3D,CAAC;KACH;IAED,MAAM,oBAAoB,GAAG,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CACvD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CACpC,CAAC;IAEF,IAAI,oBAAoB,EAAE;QACxB,OAAO;YACL,KAAK,EAAE,oBAAoB,CAAC,SAAS,CAAC,KAAc,EAAE,SAAS,CAAC;YAChE,IAAI,EAAE,oBAAoB,CAAC,UAAU;SACtC,CAAC;KACH;IAED,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF,MAAM,uBAAuB,GAA0C,EAAE,CAAC;AAC1E,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;IACzB,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;AAClD,CAAC,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAC9B,IAAS,EACT,IAAoB,EACpB,SAAoB,EACpB,EAAE;IACF,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;QACjB,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE;YACf,KAAK,QAAQ;gBACX,OAAO,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YACvD,KAAK,OAAO;gBACV,OAAO,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YACtD,KAAK,QAAQ;gBACX,OAAO,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YACvD,KAAK,aAAa;gBAChB,OAAO,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YAC3D;gBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAAC,CAAC;SACtD;KACF;SAAM;QACL,MAAM,cAAc,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAAC,CAAC;SACpD;QAED,OAAO,cAAc,CAAC,WAAW,CAAC,IAAa,EAAE,SAAS,CAAC,CAAC;KAC7D;AACH,CAAC,CAAC"}